import axios from 'axios';

// AI 视觉识别服务
export class VisionAI {
  private apiKey: string;
  private baseURL: string;
  private model: string;

  constructor() {
    this.apiKey = process.env.VISION_API_KEY || '';
    this.baseURL = process.env.VISION_BASE_URL || 'https://api.openai.com/v1';
    this.model = process.env.VISION_MODEL || 'gpt-4-vision-preview';
  }

  async analyzeImage(imageBase64: string): Promise<{ success: boolean; analysis?: any; message?: string }> {
    try {
      const response = await axios.post(`${this.baseURL}/chat/completions`, {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `请分析这张学习笔记图片，重点关注笔迹和学习内容。

**重要：科目必须从以下选项中选择一个：数学、英语、政治、计算机408**

请按照以下格式返回JSON：

{
  "subject": "数学",
  "content": "## 📸 图片分析记录 - ${new Date().toISOString().split('T')[0]}\n\n**科目：** [科目名称]\n\n**学习内容：**\n[详细的学习内容描述]\n\n**笔迹分析：**\n[观察笔迹颜色、修改痕迹、整洁程度]\n\n**正确率评估：**\n[根据修改情况估计正确率]\n\n**图片描述：**\n[对图片内容的详细描述]",
  "handwriting_analysis": "笔迹分析结果",
  "accuracy_estimate": "85%",
  "corrections_found": true,
  "confidence": 0.95
}

科目判断规则：
- 数学：数学公式、计算题、几何图形、数学符号
- 英语：英文单词、语法、英语句子、英语练习
- 政治：政治理论、马克思主义、毛概、思修内容
- 计算机408：编程代码、算法、数据结构、计算机理论

重点分析：
1. 是否有不同颜色的笔迹（红笔修改等）
2. 修改、涂改的痕迹
3. 根据修改情况评估学习正确率
4. 笔迹的整洁程度

**再次强调：subject字段必须是：数学、英语、政治、计算机408 中的一个，不能是其他值！**`
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const content = response.data.choices[0].message.content;
      
      try {
        const analysis = JSON.parse(content);
        return { success: true, analysis };
      } catch {
        // 如果不是 JSON 格式，返回原始文本
        return { 
          success: true, 
          analysis: { 
            subject: '未知', 
            content: content, 
            confidence: 0.5 
          } 
        };
      }
    } catch (error) {
      console.error('AI 视觉分析失败:', error);
      return { success: false, message: 'AI 分析失败' };
    }
  }
}

// AI 文本处理服务
export class TextAI {
  private apiKey: string;
  private baseURL: string;
  private model: string;

  constructor() {
    this.apiKey = process.env.TEXT_API_KEY || '';
    this.baseURL = process.env.TEXT_BASE_URL || 'https://api.openai.com/v1';
    this.model = process.env.TEXT_MODEL || 'gpt-4';
  }

  async generateSummary(studyData: Record<string, string>): Promise<{ success: boolean; summary?: string; message?: string }> {
    try {
      const subjects = Object.keys(studyData).filter(key => studyData[key] && studyData[key].trim());
      
      if (subjects.length === 0) {
        return { success: false, message: '没有学习内容可以总结' };
      }

      const prompt = `请基于以下学习内容生成一份简洁、有条理的学习总结：

${subjects.map(subject => `${subject}:\n${studyData[subject]}`).join('\n\n')}

请生成一份包含以下内容的总结：
1. 今日学习概览
2. 各科目重点内容
3. 知识点关联分析
4. 学习建议

总结应该简洁明了，重点突出，有助于复习和巩固。请使用纯文本格式，不要使用markdown语法。`;

      const response = await axios.post(`${this.baseURL}/chat/completions`, {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的学习助手，擅长分析和总结学习内容，帮助学生更好地理解和记忆知识点。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.7
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const summary = response.data.choices[0].message.content;
      return { success: true, summary };
    } catch (error) {
      console.error('AI 总结生成失败:', error);
      return { success: false, message: 'AI 总结生成失败' };
    }
  }

  // 生成简要学习记录
  async generateBriefRecord(analysisResult: any): Promise<{ success: boolean; briefRecord?: string; message?: string }> {
    try {
      // 首先尝试AI生成
      const response = await axios.post(`${this.baseURL}/chat/completions`, {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: `根据学习内容生成5-10字的简洁记录：

科目：${analysisResult.mappedSubject || analysisResult.subject}
内容：${analysisResult.content}

示例：
- 做了20xx年的英语阅读题，从第几页到第几页
- 背了xx个单词
- 完成数学作业，约x个题
- 复习政治知识

请只返回简洁记录：`
          }
        ],
        max_tokens: 20,
        temperature: 0.1
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      let briefRecord = response.data.choices[0].message.content.trim();

      // 如果AI返回空或无效内容，使用规则生成
      if (!briefRecord || briefRecord.length < 3) {
        briefRecord = this.generateBriefRecordByRules(analysisResult);
      }

      console.log('AI返回的简要记录:', response.data.choices[0].message.content);
      console.log('最终简要记录:', briefRecord);

      return { success: true, briefRecord };
    } catch (error) {
      console.error('生成简要记录失败:', error);
      // 如果AI调用失败，使用规则生成
      const briefRecord = this.generateBriefRecordByRules(analysisResult);
      return { success: true, briefRecord };
    }
  }

  // 基于规则生成简要记录
  private generateBriefRecordByRules(analysisResult: any): string {
    const subject = analysisResult.mappedSubject || analysisResult.subject || '';
    const content = (analysisResult.content || '').toLowerCase();

    // 根据内容关键词判断活动类型
    if (content.includes('阅读') || content.includes('reading')) {
      return `做了${subject}阅读题`;
    } else if (content.includes('单词') || content.includes('word')) {
      return `背了${subject}单词`;
    } else if (content.includes('作业') || content.includes('homework')) {
      return `完成了${subject}作业`;
    } else if (content.includes('练习') || content.includes('exercise')) {
      return `做了${subject}练习`;
    } else if (content.includes('复习') || content.includes('review')) {
      return `复习了${subject}知识`;
    } else if (content.includes('选择题') || content.includes('题目')) {
      return `做了${subject}题目`;
    } else {
      return `学习了${subject}`;
    }
  }

  // 让文本AI重新分析图片AI的原始输出
  async formatDetailedAnalysis(analysisResult: any): Promise<{ success: boolean; formattedContent?: string; message?: string }> {
    try {
      console.log('传递给文本AI的原始数据:', analysisResult);

      const response = await axios.post(`${this.baseURL}/chat/completions`, {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: `请分析以下图片AI的分析结果，并重新整理成简洁易读的文字格式：

${JSON.stringify(analysisResult, null, 2)}

请按以下格式输出：

**学习内容：**
[根据AI分析结果描述学习的具体内容]

**笔迹分析：**
[描述笔迹情况、修改痕迹等]

**学习评估：**
[描述正确率、学习效果等]

请确保输出是纯文字格式，不要包含JSON。`
          }
        ],
        max_tokens: 400,
        temperature: 0.1
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const formattedContent = response.data.choices[0].message.content.trim();
      console.log('文本AI重新分析结果:', formattedContent);

      // 如果AI返回空内容，使用备用格式
      if (!formattedContent || formattedContent.length < 10) {
        console.log('文本AI返回内容太短，使用备用格式');
        const fallbackContent = this.generateFallbackContent(analysisResult);
        console.log('使用备用格式:', fallbackContent);
        return { success: true, formattedContent: fallbackContent };
      }

      return { success: true, formattedContent };
    } catch (error) {
      console.error('文本AI重新分析失败:', error);
      // 如果AI调用失败，使用备用格式
      const fallbackContent = this.generateFallbackContent(analysisResult);
      return { success: true, formattedContent: fallbackContent };
    }
  }

  // 生成备用格式化内容
  private generateFallbackContent(analysisResult: any): string {
    const subject = analysisResult.mappedSubject || analysisResult.subject || '未知科目';
    const handwritingAnalysis = analysisResult.handwriting_analysis || '笔迹分析信息不可用';
    const accuracyEstimate = analysisResult.accuracy_estimate || '正确率信息不可用';

    return `**学习内容：**
完成了${subject}相关的学习任务，包含图片分析和内容识别。

**笔迹分析：**
${handwritingAnalysis}

**学习评估：**
估计正确率：${accuracyEstimate}

**备注：**
详细分析内容已保存，可在Notion页面中查看完整信息。`;
  }
}

export const visionAI = new VisionAI();
export const textAI = new TextAI();
