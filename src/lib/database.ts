import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { AISettings, AIConfig, DEFAULT_AI_SETTINGS } from '@/types/ai-config';

// 数据库管理器
class DatabaseManager {
  private static instance: DatabaseManager;
  private db: Database.Database;

  private constructor() {
    // 确保数据目录存在
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // 初始化数据库
    const dbPath = path.join(dataDir, 'ai-config.db');
    this.db = new Database(dbPath);
    this.initTables();
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  private initTables(): void {
    // 创建AI配置表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS ai_configs (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        name TEXT NOT NULL,
        provider TEXT NOT NULL,
        api_key TEXT NOT NULL,
        base_url TEXT NOT NULL,
        model TEXT NOT NULL,
        enabled INTEGER NOT NULL DEFAULT 1,
        priority INTEGER NOT NULL DEFAULT 1,
        max_tokens INTEGER,
        temperature REAL,
        timeout INTEGER,
        rate_limit_per_minute INTEGER,
        rate_limit_per_hour INTEGER,
        error_count INTEGER NOT NULL DEFAULT 0,
        total_requests INTEGER NOT NULL DEFAULT 0,
        success_requests INTEGER NOT NULL DEFAULT 0,
        last_used TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // 创建AI设置表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS ai_settings (
        id INTEGER PRIMARY KEY,
        text_strategy TEXT NOT NULL DEFAULT 'failover',
        text_fallback_enabled INTEGER NOT NULL DEFAULT 1,
        text_max_retries INTEGER NOT NULL DEFAULT 3,
        text_retry_delay INTEGER NOT NULL DEFAULT 1000,
        text_current_index INTEGER NOT NULL DEFAULT 0,
        vision_strategy TEXT NOT NULL DEFAULT 'failover',
        vision_fallback_enabled INTEGER NOT NULL DEFAULT 1,
        vision_max_retries INTEGER NOT NULL DEFAULT 3,
        vision_retry_delay INTEGER NOT NULL DEFAULT 1000,
        vision_current_index INTEGER NOT NULL DEFAULT 0,
        enable_logging INTEGER NOT NULL DEFAULT 1,
        log_level TEXT NOT NULL DEFAULT 'info',
        enable_metrics INTEGER NOT NULL DEFAULT 1,
        auto_switch_on_error INTEGER NOT NULL DEFAULT 1,
        health_check_interval INTEGER NOT NULL DEFAULT 30,
        version TEXT NOT NULL DEFAULT '1.0.0',
        updated_at TEXT NOT NULL
      )
    `);

    // 插入默认设置（如果不存在）
    const existingSettings = this.db.prepare('SELECT COUNT(*) as count FROM ai_settings').get() as { count: number };
    if (existingSettings.count === 0) {
      this.db.prepare(`
        INSERT INTO ai_settings (
          text_strategy, text_fallback_enabled, text_max_retries, text_retry_delay,
          vision_strategy, vision_fallback_enabled, vision_max_retries, vision_retry_delay,
          enable_logging, log_level, enable_metrics, auto_switch_on_error, health_check_interval,
          version, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'failover', 1, 3, 1000,
        'failover', 1, 3, 1000,
        1, 'info', 1, 1, 30,
        '1.0.0', new Date().toISOString()
      );
    }
  }

  // 获取所有AI配置
  public getAIConfigs(type?: 'text' | 'vision'): AIConfig[] {
    const query = type 
      ? this.db.prepare('SELECT * FROM ai_configs WHERE type = ? ORDER BY priority ASC')
      : this.db.prepare('SELECT * FROM ai_configs ORDER BY type, priority ASC');
    
    const rows = type ? query.all(type) : query.all();
    
    return (rows as any[]).map(row => ({
      id: row.id,
      name: row.name,
      provider: row.provider as any,
      apiKey: row.api_key,
      baseURL: row.base_url,
      model: row.model,
      enabled: Boolean(row.enabled),
      priority: row.priority,
      maxTokens: row.max_tokens,
      temperature: row.temperature,
      timeout: row.timeout,
      rateLimit: row.rate_limit_per_minute ? {
        requestsPerMinute: row.rate_limit_per_minute,
        requestsPerHour: row.rate_limit_per_hour
      } : undefined,
      errorCount: row.error_count,
      totalRequests: row.total_requests,
      successRequests: row.success_requests,
      lastUsed: row.last_used ? new Date(row.last_used) : undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }));
  }

  // 添加AI配置
  public addAIConfig(type: 'text' | 'vision', config: Omit<AIConfig, 'id' | 'createdAt' | 'updatedAt' | 'errorCount' | 'totalRequests' | 'successRequests'>): string {
    const id = Date.now().toString(36) + Math.random().toString(36).substr(2);
    const now = new Date().toISOString();

    this.db.prepare(`
      INSERT INTO ai_configs (
        id, type, name, provider, api_key, base_url, model, enabled, priority,
        max_tokens, temperature, timeout, rate_limit_per_minute, rate_limit_per_hour,
        error_count, total_requests, success_requests, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id, type, config.name, config.provider, config.apiKey, config.baseURL, config.model,
      config.enabled ? 1 : 0, config.priority, config.maxTokens, config.temperature, config.timeout,
      config.rateLimit?.requestsPerMinute, config.rateLimit?.requestsPerHour,
      0, 0, 0, now, now
    );

    return id;
  }

  // 更新AI配置
  public updateAIConfig(configId: string, updates: Partial<AIConfig>): boolean {
    const now = new Date().toISOString();
    
    const fields: string[] = [];
    const values: any[] = [];

    if (updates.name !== undefined) { fields.push('name = ?'); values.push(updates.name); }
    if (updates.provider !== undefined) { fields.push('provider = ?'); values.push(updates.provider); }
    if (updates.apiKey !== undefined) { fields.push('api_key = ?'); values.push(updates.apiKey); }
    if (updates.baseURL !== undefined) { fields.push('base_url = ?'); values.push(updates.baseURL); }
    if (updates.model !== undefined) { fields.push('model = ?'); values.push(updates.model); }
    if (updates.enabled !== undefined) { fields.push('enabled = ?'); values.push(updates.enabled ? 1 : 0); }
    if (updates.priority !== undefined) { fields.push('priority = ?'); values.push(updates.priority); }
    if (updates.maxTokens !== undefined) { fields.push('max_tokens = ?'); values.push(updates.maxTokens); }
    if (updates.temperature !== undefined) { fields.push('temperature = ?'); values.push(updates.temperature); }
    if (updates.timeout !== undefined) { fields.push('timeout = ?'); values.push(updates.timeout); }
    if (updates.errorCount !== undefined) { fields.push('error_count = ?'); values.push(updates.errorCount); }
    if (updates.totalRequests !== undefined) { fields.push('total_requests = ?'); values.push(updates.totalRequests); }
    if (updates.successRequests !== undefined) { fields.push('success_requests = ?'); values.push(updates.successRequests); }
    if (updates.lastUsed !== undefined) { fields.push('last_used = ?'); values.push(updates.lastUsed.toISOString()); }

    if (fields.length === 0) return false;

    fields.push('updated_at = ?');
    values.push(now);
    values.push(configId);

    const result = this.db.prepare(`
      UPDATE ai_configs SET ${fields.join(', ')} WHERE id = ?
    `).run(...values);

    return result.changes > 0;
  }

  // 删除AI配置
  public deleteAIConfig(configId: string): boolean {
    const result = this.db.prepare('DELETE FROM ai_configs WHERE id = ?').run(configId);
    return result.changes > 0;
  }

  // 获取AI设置
  public getAISettings(): AISettings {
    const row = this.db.prepare('SELECT * FROM ai_settings LIMIT 1').get() as any;
    
    if (!row) {
      return DEFAULT_AI_SETTINGS;
    }

    const textConfigs = this.getAIConfigs('text');
    const visionConfigs = this.getAIConfigs('vision');

    return {
      textAI: {
        type: 'text',
        strategy: row.text_strategy as any,
        configs: textConfigs,
        currentIndex: row.text_current_index,
        fallbackEnabled: Boolean(row.text_fallback_enabled),
        maxRetries: row.text_max_retries,
        retryDelay: row.text_retry_delay
      },
      visionAI: {
        type: 'vision',
        strategy: row.vision_strategy as any,
        configs: visionConfigs,
        currentIndex: row.vision_current_index,
        fallbackEnabled: Boolean(row.vision_fallback_enabled),
        maxRetries: row.vision_max_retries,
        retryDelay: row.vision_retry_delay
      },
      globalSettings: {
        enableLogging: Boolean(row.enable_logging),
        logLevel: row.log_level as any,
        enableMetrics: Boolean(row.enable_metrics),
        autoSwitchOnError: Boolean(row.auto_switch_on_error),
        healthCheckInterval: row.health_check_interval
      },
      version: row.version,
      lastUpdated: new Date(row.updated_at)
    };
  }

  // 更新AI设置
  public updateAISettings(updates: Partial<AISettings>): void {
    const now = new Date().toISOString();
    const fields: string[] = [];
    const values: any[] = [];

    if (updates.textAI) {
      if (updates.textAI.strategy !== undefined) { fields.push('text_strategy = ?'); values.push(updates.textAI.strategy); }
      if (updates.textAI.fallbackEnabled !== undefined) { fields.push('text_fallback_enabled = ?'); values.push(updates.textAI.fallbackEnabled ? 1 : 0); }
      if (updates.textAI.maxRetries !== undefined) { fields.push('text_max_retries = ?'); values.push(updates.textAI.maxRetries); }
      if (updates.textAI.retryDelay !== undefined) { fields.push('text_retry_delay = ?'); values.push(updates.textAI.retryDelay); }
      if (updates.textAI.currentIndex !== undefined) { fields.push('text_current_index = ?'); values.push(updates.textAI.currentIndex); }
    }

    if (updates.visionAI) {
      if (updates.visionAI.strategy !== undefined) { fields.push('vision_strategy = ?'); values.push(updates.visionAI.strategy); }
      if (updates.visionAI.fallbackEnabled !== undefined) { fields.push('vision_fallback_enabled = ?'); values.push(updates.visionAI.fallbackEnabled ? 1 : 0); }
      if (updates.visionAI.maxRetries !== undefined) { fields.push('vision_max_retries = ?'); values.push(updates.visionAI.maxRetries); }
      if (updates.visionAI.retryDelay !== undefined) { fields.push('vision_retry_delay = ?'); values.push(updates.visionAI.retryDelay); }
      if (updates.visionAI.currentIndex !== undefined) { fields.push('vision_current_index = ?'); values.push(updates.visionAI.currentIndex); }
    }

    if (updates.globalSettings) {
      if (updates.globalSettings.enableLogging !== undefined) { fields.push('enable_logging = ?'); values.push(updates.globalSettings.enableLogging ? 1 : 0); }
      if (updates.globalSettings.logLevel !== undefined) { fields.push('log_level = ?'); values.push(updates.globalSettings.logLevel); }
      if (updates.globalSettings.enableMetrics !== undefined) { fields.push('enable_metrics = ?'); values.push(updates.globalSettings.enableMetrics ? 1 : 0); }
      if (updates.globalSettings.autoSwitchOnError !== undefined) { fields.push('auto_switch_on_error = ?'); values.push(updates.globalSettings.autoSwitchOnError ? 1 : 0); }
      if (updates.globalSettings.healthCheckInterval !== undefined) { fields.push('health_check_interval = ?'); values.push(updates.globalSettings.healthCheckInterval); }
    }

    if (fields.length === 0) return;

    fields.push('updated_at = ?');
    values.push(now);

    this.db.prepare(`
      UPDATE ai_settings SET ${fields.join(', ')} WHERE id = 1
    `).run(...values);
  }

  // 关闭数据库连接
  public close(): void {
    this.db.close();
  }
}

// 导出单例实例
export const database = DatabaseManager.getInstance();
