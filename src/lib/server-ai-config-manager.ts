import { database } from '@/lib/database';
import { 
  AISettings, 
  AIConfig, 
  AIConfigGroup, 
  CallStrategy, 
  DEFAULT_AI_SETTINGS
} from '@/types/ai-config';

// 服务器端AI配置管理器
export class ServerAIConfigManager {
  private static instance: ServerAIConfigManager;

  private constructor() {}

  public static getInstance(): ServerAIConfigManager {
    if (!ServerAIConfigManager.instance) {
      ServerAIConfigManager.instance = new ServerAIConfigManager();
    }
    return ServerAIConfigManager.instance;
  }

  // 获取配置
  public getSettings(): AISettings {
    return database.getAISettings();
  }

  // 更新配置
  public updateSettings(newSettings: Partial<AISettings>): AISettings {
    database.updateAISettings(newSettings);
    return database.getAISettings();
  }

  // 添加AI配置
  public addConfig(type: 'text' | 'vision', config: Omit<AIConfig, 'id' | 'createdAt' | 'updatedAt' | 'errorCount' | 'totalRequests' | 'successRequests'>): string {
    return database.addAIConfig(type, config);
  }

  // 更新AI配置
  public updateConfig(configId: string, updates: Partial<AIConfig>): boolean {
    return database.updateAIConfig(configId, updates);
  }

  // 删除AI配置
  public deleteConfig(configId: string): boolean {
    return database.deleteAIConfig(configId);
  }

  // 获取指定类型的配置组
  public getConfigGroup(type: 'text' | 'vision'): AIConfigGroup {
    const settings = this.getSettings();
    return type === 'text' ? settings.textAI : settings.visionAI;
  }

  // 根据策略选择配置
  public selectConfig(type: 'text' | 'vision', options?: { forceConfigId?: string }): AIConfig | null {
    const group = this.getConfigGroup(type);
    const availableConfigs = group.configs.filter(c => c.enabled);

    if (availableConfigs.length === 0) return null;

    // 如果指定了配置ID
    if (options?.forceConfigId) {
      return availableConfigs.find(c => c.id === options.forceConfigId) || null;
    }

    switch (group.strategy) {
      case 'manual':
        return availableConfigs[0] || null;

      case 'round_robin':
        const config = availableConfigs[group.currentIndex % availableConfigs.length];
        // 更新当前索引
        const newIndex = (group.currentIndex + 1) % availableConfigs.length;
        this.updateSettings({
          [type === 'text' ? 'textAI' : 'visionAI']: {
            ...group,
            currentIndex: newIndex
          }
        });
        return config;

      case 'failover':
        // 按优先级排序，选择第一个可用的
        return availableConfigs
          .sort((a, b) => a.priority - b.priority)
          .find(c => c.errorCount < 5) || availableConfigs[0];

      case 'random':
        return availableConfigs[Math.floor(Math.random() * availableConfigs.length)];

      case 'priority':
        return availableConfigs
          .sort((a, b) => a.priority - b.priority)[0];

      default:
        return availableConfigs[0];
    }
  }

  // 记录API调用结果
  public recordCallResult(configId: string, success: boolean, responseTime?: number): void {
    const allConfigs = [
      ...database.getAIConfigs('text'),
      ...database.getAIConfigs('vision')
    ];
    const config = allConfigs.find(c => c.id === configId);
    
    if (!config) return;

    const updates: any = {
      totalRequests: config.totalRequests + 1,
      lastUsed: new Date()
    };

    if (success) {
      updates.successRequests = config.successRequests + 1;
      updates.errorCount = 0; // 重置错误计数
    } else {
      updates.errorCount = config.errorCount + 1;
    }

    database.updateAIConfig(configId, updates);
  }

  // 获取配置统计信息
  public getConfigStats(type?: 'text' | 'vision'): Array<{
    configId: string;
    name: string;
    totalRequests: number;
    successRequests: number;
    errorRequests: number;
    successRate: number;
    averageResponseTime: number;
    lastUsed?: Date;
    status: 'active' | 'inactive' | 'error' | 'disabled';
  }> {
    let configs: AIConfig[] = [];
    
    if (type === 'text') {
      configs = database.getAIConfigs('text');
    } else if (type === 'vision') {
      configs = database.getAIConfigs('vision');
    } else {
      configs = [...database.getAIConfigs('text'), ...database.getAIConfigs('vision')];
    }

    return configs.map(config => ({
      configId: config.id,
      name: config.name,
      totalRequests: config.totalRequests,
      successRequests: config.successRequests,
      errorRequests: config.totalRequests - config.successRequests,
      successRate: config.totalRequests > 0 ? (config.successRequests / config.totalRequests) * 100 : 0,
      averageResponseTime: 0, // TODO: 实现响应时间统计
      lastUsed: config.lastUsed,
      status: !config.enabled ? 'disabled' : 
              config.errorCount >= 5 ? 'error' : 
              config.totalRequests > 0 ? 'active' : 'inactive'
    }));
  }
}

// 导出单例实例
export const serverAIConfigManager = ServerAIConfigManager.getInstance();
