import { aiConfigManager } from './ai-config-manager';
import { serverAIConfigManager } from './server-ai-config-manager';
import { AIConfig, AICallResult, AICallOptions } from '@/types/ai-config';
import { enhancedVisionAI, enhancedTextAI } from './ai-service';

// AI调用管理器
export class AICaller {
  private static instance: AICaller;

  private constructor() {}

  public static getInstance(): AICaller {
    if (!AICaller.instance) {
      AICaller.instance = new AICaller();
    }
    return AICaller.instance;
  }

  // 调用文本AI
  async callTextAI(
    action: 'generateSummary' | 'generateBriefRecord' | 'formatDetailedAnalysis',
    data: any,
    options?: AICallOptions
  ): Promise<AICallResult> {
    return this.callAI('text', action, data, options);
  }

  // 调用视觉AI
  async callVisionAI(
    action: 'analyzeImage',
    data: any,
    options?: AICallOptions
  ): Promise<AICallResult> {
    return this.callAI('vision', action, data, options);
  }

  // 通用AI调用方法
  private async callAI(
    type: 'text' | 'vision',
    action: string,
    data: any,
    options?: AICallOptions
  ): Promise<AICallResult> {
    // 根据环境选择配置管理器
    const configManager = typeof window === 'undefined' ? serverAIConfigManager : aiConfigManager;

    let group;
    if (typeof window === 'undefined') {
      // 服务器端
      group = serverAIConfigManager.getConfigGroup(type);
    } else {
      // 客户端
      const settings = await aiConfigManager.getSettings();
      group = type === 'text' ? settings.textAI : settings.visionAI;
    }
    
    if (group.configs.length === 0) {
      return {
        success: false,
        error: `没有可用的${type === 'text' ? '文本' : '视觉'}AI配置`
      };
    }

    // 如果指定了配置ID，直接使用
    if (options?.forceConfigId) {
      const config = group.configs.find(c => c.id === options.forceConfigId);
      if (!config) {
        return {
          success: false,
          error: '指定的配置不存在'
        };
      }
      return this.makeAPICall(type, action, config, data, options);
    }

    // 根据策略选择配置
    const availableConfigs = group.configs.filter(c => c.enabled);
    if (availableConfigs.length === 0) {
      return {
        success: false,
        error: `没有启用的${type === 'text' ? '文本' : '视觉'}AI配置`
      };
    }

    // 尝试调用，支持故障转移
    let lastError = '';
    const maxAttempts = group.fallbackEnabled ? Math.min(availableConfigs.length, 3) : 1;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const config = this.selectConfigFromGroup(group, options);
      if (!config) {
        lastError = '无法选择可用配置';
        continue;
      }

      const result = await this.makeAPICall(type, action, config, data, options);
      
      // 记录调用结果
      if (typeof window === 'undefined') {
        serverAIConfigManager.recordCallResult(config.id, result.success, result.responseTime);
      } else {
        aiConfigManager.recordCallResult(config.id, result.success, result.responseTime).catch(console.error);
      }
      
      if (result.success) {
        return result;
      }

      lastError = result.error || '调用失败';
      
      // 如果不启用故障转移，直接返回
      if (!group.fallbackEnabled || options?.skipFailover) {
        break;
      }

      // 如果是不应该重试的错误（如认证错误），直接返回
      if (this.shouldNotRetry(result.error)) {
        break;
      }

      console.warn(`配置 ${config.name} 调用失败，尝试下一个配置:`, result.error);
    }

    return {
      success: false,
      error: lastError || '所有配置都调用失败'
    };
  }

  // 从配置组中选择配置
  private selectConfigFromGroup(group: any, options?: AICallOptions): any {
    const availableConfigs = group.configs.filter((c: any) => c.enabled);

    if (availableConfigs.length === 0) return null;

    // 如果指定了配置ID
    if (options?.forceConfigId) {
      return availableConfigs.find((c: any) => c.id === options.forceConfigId) || null;
    }

    switch (group.strategy) {
      case 'manual':
        return availableConfigs[0] || null;

      case 'round_robin':
        const config = availableConfigs[group.currentIndex % availableConfigs.length];
        group.currentIndex = (group.currentIndex + 1) % availableConfigs.length;
        return config;

      case 'failover':
        // 按优先级排序，选择第一个可用的
        return availableConfigs
          .sort((a: any, b: any) => a.priority - b.priority)
          .find((c: any) => c.errorCount < 5) || availableConfigs[0];

      case 'random':
        return availableConfigs[Math.floor(Math.random() * availableConfigs.length)];

      case 'priority':
        return availableConfigs
          .sort((a: any, b: any) => a.priority - b.priority)[0];

      default:
        return availableConfigs[0];
    }
  }

  // 实际的API调用
  private async makeAPICall(
    type: 'text' | 'vision',
    action: string,
    config: AIConfig,
    data: any,
    options?: AICallOptions
  ): Promise<AICallResult> {
    try {
      // 在服务器端直接调用AI服务
      if (typeof window === 'undefined') {
        return await this.directAICall(type, action, config, data, options);
      }

      // 在客户端使用fetch
      const response = await fetch('/api/ai/call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          action,
          config,
          data,
          options
        })
      });

      const result = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: result.message || `HTTP ${response.status}`,
          configId: config.id
        };
      }

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        configId: result.metadata?.configId || config.id,
        responseTime: result.metadata?.responseTime,
        retryCount: result.metadata?.retryCount,
        tokensUsed: result.metadata?.tokensUsed
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '网络错误',
        configId: config.id
      };
    }
  }

  // 服务器端直接调用AI服务
  private async directAICall(
    type: 'text' | 'vision',
    action: string,
    config: AIConfig,
    data: any,
    options?: AICallOptions
  ): Promise<AICallResult> {
    try {
      if (type === 'vision') {
        switch (action) {
          case 'analyzeImage':
            if (!data.imageBase64) {
              return { success: false, error: '缺少图片数据' };
            }
            return await enhancedVisionAI.analyzeImage(data.imageBase64, config, options);
          default:
            return { success: false, error: '不支持的视觉AI操作' };
        }
      } else if (type === 'text') {
        switch (action) {
          case 'generateSummary':
            if (!data.studyData) {
              return { success: false, error: '缺少学习数据' };
            }
            return await enhancedTextAI.generateSummary(data.studyData, config, options);
          case 'generateBriefRecord':
            if (!data.analysisResult) {
              return { success: false, error: '缺少分析结果' };
            }
            return await enhancedTextAI.generateBriefRecord(data.analysisResult, config, options);
          case 'formatDetailedAnalysis':
            if (!data.analysisResult) {
              return { success: false, error: '缺少分析结果' };
            }
            return await enhancedTextAI.formatDetailedAnalysis(data.analysisResult, config, options);
          default:
            return { success: false, error: '不支持的文本AI操作' };
        }
      } else {
        return { success: false, error: '不支持的AI类型' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '服务器错误',
        configId: config.id
      };
    }
  }

  // 判断是否应该重试
  private shouldNotRetry(error?: string): boolean {
    if (!error) return false;
    
    const noRetryErrors = [
      'API密钥无效',
      '未授权访问',
      'API访问被拒绝',
      '缺少必要参数',
      '不支持的操作'
    ];
    
    return noRetryErrors.some(noRetryError => error.includes(noRetryError));
  }

  // 测试配置
  async testConfig(config: AIConfig, type: 'text' | 'vision'): Promise<AICallResult> {
    const testData = type === 'text' 
      ? { studyData: { '测试': '这是一个测试内容' } }
      : { imageBase64: 'test' }; // 这里应该是一个真实的base64图片

    const testAction = type === 'text' ? 'generateSummary' : 'analyzeImage';
    
    return this.makeAPICall(type, testAction, config, testData, { 
      maxRetries: 1,
      timeout: 10000,
      skipFailover: true
    });
  }

  // 获取配置健康状态
  getConfigHealth(type?: 'text' | 'vision'): Array<{
    configId: string;
    name: string;
    healthy: boolean;
    lastError?: string;
    successRate: number;
  }> {
    const stats = aiConfigManager.getConfigStats(type);
    
    return stats.map(stat => ({
      configId: stat.configId,
      name: stat.name,
      healthy: stat.status === 'active' && stat.successRate > 50,
      successRate: stat.successRate,
      lastError: stat.status === 'error' ? '连续错误过多' : undefined
    }));
  }

  // 重置配置错误计数
  resetConfigErrors(configId: string): void {
    const allConfigs = [
      ...aiConfigManager.getConfigGroup('text').configs,
      ...aiConfigManager.getConfigGroup('vision').configs
    ];
    
    const config = allConfigs.find(c => c.id === configId);
    if (config) {
      const type = aiConfigManager.getConfigGroup('text').configs.includes(config) ? 'text' : 'vision';
      aiConfigManager.updateConfig(type, configId, { errorCount: 0 });
    }
  }
}

// 导出单例实例
export const aiCaller = AICaller.getInstance();

// 便捷方法
export const callTextAI = aiCaller.callTextAI.bind(aiCaller);
export const callVisionAI = aiCaller.callVisionAI.bind(aiCaller);
export const testAIConfig = aiCaller.testConfig.bind(aiCaller);
