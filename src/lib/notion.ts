import axios from 'axios';

const NOTION_API_BASE = 'https://api.notion.com/v1';

interface NotionClient {
  token: string;
  databaseId: string;
}

class NotionService {
  private client: NotionClient;

  constructor() {
    this.client = {
      token: process.env.NOTION_TOKEN || '',
      databaseId: process.env.DATABASE_ID || ''
    };
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.client.token}`,
      'Content-Type': 'application/json',
      'Notion-Version': '2022-06-28'
    };
  }

  // 获取今日数据
  async getTodayData(): Promise<{ success: boolean; data?: any; date?: string; message?: string }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const response = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
        filter: {
          property: '日期',
          date: {
            equals: today
          }
        }
      }, { headers: this.getHeaders() });

      const results = response.data.results;
      
      if (results.length === 0) {
        return { success: true, data: {}, date: today };
      }

      const record = results[0];
      const data: Record<string, string> = {};

      // 提取各科目数据 - 考研科目
      const subjects = ['数学', '英语', '政治', '计算机408'];
      subjects.forEach(subject => {
        const property = record.properties[subject];
        if (property && property.rich_text && property.rich_text.length > 0) {
          data[subject] = property.rich_text[0].plain_text;
        }
      });

      return { success: true, data, date: today };
    } catch (error) {
      console.error('获取今日数据失败:', error);
      return { success: false, message: '获取数据失败' };
    }
  }

  // 更新学习记录
  async updateStudyRecord(subject: string, content: string, mode: 'replace' | 'append' = 'replace'): Promise<{ success: boolean; message?: string }> {
    try {
      const today = new Date().toISOString().split('T')[0];

      // 查找今日记录
      const queryResponse = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
        filter: {
          property: '日期',
          date: {
            equals: today
          }
        }
      }, { headers: this.getHeaders() });

      let pageId: string;
      let existingContent = '';

      if (queryResponse.data.results.length > 0) {
        // 更新现有记录
        pageId = queryResponse.data.results[0].id;

        if (mode === 'append') {
          const existingProperty = queryResponse.data.results[0].properties[subject];
          if (existingProperty && existingProperty.rich_text && existingProperty.rich_text.length > 0) {
            existingContent = existingProperty.rich_text[0].plain_text;
          }
        }
      } else {
        // 创建新记录 - 包含title属性
        const createProperties: any = {
          '日期': {
            date: { start: today }
          }
        };

        // 如果数据库有title属性，设置默认标题
        try {
          createProperties['title'] = {
            title: [
              {
                text: { content: `学习记录 - ${today}` }
              }
            ]
          };
        } catch (e) {
          // 如果title属性不存在，忽略错误
        }

        const createResponse = await axios.post(`${NOTION_API_BASE}/pages`, {
          parent: { database_id: this.client.databaseId },
          properties: createProperties
        }, { headers: this.getHeaders() });

        pageId = createResponse.data.id;
      }

      // 更新内容
      let finalContent = content;
      if (mode === 'append' && existingContent) {
        // 如果现有内容不为空，用逗号分隔
        finalContent = existingContent.trim().endsWith(',')
          ? `${existingContent} ${content}`
          : `${existingContent}, ${content}`;
      }

      const updateProperties: any = {
        [subject]: {
          rich_text: [
            {
              text: { content: finalContent }
            }
          ]
        }
      };

      await axios.patch(`${NOTION_API_BASE}/pages/${pageId}`, {
        properties: updateProperties
      }, { headers: this.getHeaders() });

      return { success: true, message: '更新成功' };
    } catch (error: any) {
      console.error('更新学习记录失败:', error);

      // 提供更详细的错误信息
      let errorMessage = '更新失败';
      if (error.response) {
        console.error('Notion API 错误响应:', error.response.data);
        if (error.response.status === 400) {
          errorMessage = `数据库属性错误: ${error.response.data?.message || '请检查Notion数据库属性配置'}`;
        } else if (error.response.status === 401) {
          errorMessage = 'Notion API 认证失败，请检查token';
        } else if (error.response.status === 404) {
          errorMessage = '未找到指定的数据库，请检查DATABASE_ID';
        }
      }

      return { success: false, message: errorMessage };
    }
  }

  // 获取数据库结构信息（用于调试）
  async getDatabaseSchema(): Promise<{ success: boolean; schema?: any; message?: string }> {
    try {
      const response = await axios.get(`${NOTION_API_BASE}/databases/${this.client.databaseId}`, {
        headers: this.getHeaders()
      });

      const properties = response.data.properties;
      console.log('Notion数据库属性:', Object.keys(properties));

      return {
        success: true,
        schema: properties,
        message: `数据库包含以下属性: ${Object.keys(properties).join(', ')}`
      };
    } catch (error: any) {
      console.error('获取数据库结构失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '获取数据库结构失败'
      };
    }
  }

  // 更新页面内容
  async updatePageContent(formattedContent: string, imageUrl?: string): Promise<{ success: boolean; message?: string }> {
    try {
      const today = new Date().toISOString().split('T')[0];

      // 查找今日记录
      const queryResponse = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
        filter: {
          property: '日期',
          date: {
            equals: today
          }
        }
      }, { headers: this.getHeaders() });

      if (queryResponse.data.results.length === 0) {
        return { success: false, message: '未找到今日记录' };
      }

      const pageId = queryResponse.data.results[0].id;

      // 构建页面内容块
      const blocks = [
        {
          object: 'block',
          type: 'heading_2',
          heading_2: {
            rich_text: [
              {
                type: 'text',
                text: {
                  content: `📚 学习分析 - ${new Date().toLocaleString('zh-CN')}`
                }
              }
            ]
          }
        },
        {
          object: 'block',
          type: 'paragraph',
          paragraph: {
            rich_text: [
              {
                type: 'text',
                text: {
                  content: formattedContent
                }
              }
            ]
          }
        }
      ];

      // 如果有图片，添加图片
      if (imageUrl) {
        blocks.push({
          object: 'block',
          type: 'divider',
          divider: {}
        });

        blocks.push({
          object: 'block',
          type: 'heading_3',
          heading_3: {
            rich_text: [
              {
                type: 'text',
                text: {
                  content: '📸 学习图片'
                }
              }
            ]
          }
        });

        blocks.push({
          object: 'block',
          type: 'image',
          image: {
            type: 'external',
            external: {
              url: imageUrl
            }
          }
        });
      }

      // 添加内容到页面
      await axios.patch(`${NOTION_API_BASE}/blocks/${pageId}/children`, {
        children: blocks
      }, { headers: this.getHeaders() });

      return { success: true, message: '页面内容更新成功' };
    } catch (error: any) {
      console.error('更新页面内容失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '更新页面内容失败'
      };
    }
  }

  // 保存 AI 总结
  async saveSummary(summary: string): Promise<{ success: boolean; message?: string }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // 查找今日记录
      const queryResponse = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
        filter: {
          property: '日期',
          date: {
            equals: today
          }
        }
      }, { headers: this.getHeaders() });

      if (queryResponse.data.results.length === 0) {
        return { success: false, message: '未找到今日记录' };
      }

      const pageId = queryResponse.data.results[0].id;

      // 更新总结字段 - 使用正确的字段名称
      await axios.patch(`${NOTION_API_BASE}/pages/${pageId}`, {
        properties: {
          '总结（AI生成）': {
            rich_text: [
              {
                text: { content: summary.substring(0, 2000) } // 限制长度防止超出Notion限制
              }
            ]
          }
        }
      }, { headers: this.getHeaders() });

      return { success: true, message: '总结保存成功' };
    } catch (error) {
      console.error('保存总结失败:', error);
      return { success: false, message: '保存总结失败' };
    }
  }
}

export const notionService = new NotionService();
