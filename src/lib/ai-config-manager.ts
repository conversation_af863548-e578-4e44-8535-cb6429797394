import {
  AISettings,
  AIConfig,
  AIConfigGroup,
  CallStrategy,
  AICallResult,
  AICallOptions,
  ConfigValidationResult,
  ConfigStats,
  DEFAULT_AI_SETTINGS,
  AI_CONFIG_TEMPLATES
} from '@/types/ai-config';

// AI配置管理器 - 客户端版本（仍使用localStorage作为缓存）
export class AIConfigManager {
  private static instance: AIConfigManager;
  private settings: AISettings | null = null;
  private readonly STORAGE_KEY = 'ai-settings';

  private constructor() {
    // 客户端初始化时加载localStorage缓存
    if (typeof window !== 'undefined') {
      this.loadFromLocalStorage();
    }
  }

  public static getInstance(): AIConfigManager {
    if (!AIConfigManager.instance) {
      AIConfigManager.instance = new AIConfigManager();
    }
    return AIConfigManager.instance;
  }

  // 从localStorage加载缓存（仅客户端）
  private loadFromLocalStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.settings = {
          ...DEFAULT_AI_SETTINGS,
          ...parsed,
          textAI: { ...DEFAULT_AI_SETTINGS.textAI, ...parsed.textAI },
          visionAI: { ...DEFAULT_AI_SETTINGS.visionAI, ...parsed.visionAI },
          globalSettings: { ...DEFAULT_AI_SETTINGS.globalSettings, ...parsed.globalSettings }
        };
      }
    } catch (error) {
      console.error('加载AI配置缓存失败:', error);
    }
  }

  // 保存到localStorage缓存（仅客户端）
  private saveToLocalStorage(): void {
    if (typeof window === 'undefined' || !this.settings) return;

    try {
      this.settings.lastUpdated = new Date();
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings));
    } catch (error) {
      console.error('保存AI配置缓存失败:', error);
    }
  }

  // 从服务器加载配置
  private async loadFromServer(): Promise<AISettings> {
    try {
      const response = await fetch('/api/ai/config');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          this.settings = data.data;
          this.saveToLocalStorage(); // 更新本地缓存
          return this.settings;
        }
      }
    } catch (error) {
      console.error('从服务器加载AI配置失败:', error);
    }

    // 如果加载失败，返回默认设置
    this.settings = DEFAULT_AI_SETTINGS;
    return this.settings;
  }

  // 获取当前配置
  public async getSettings(): Promise<AISettings> {
    if (!this.settings) {
      await this.loadFromServer();
    }
    return { ...this.settings! };
  }

  // 同步获取配置（使用缓存）
  public getSettingsSync(): AISettings {
    return this.settings ? { ...this.settings } : DEFAULT_AI_SETTINGS;
  }

  // 更新配置
  public async updateSettings(newSettings: Partial<AISettings>): Promise<void> {
    try {
      const response = await fetch('/api/ai/config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSettings)
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          this.settings = data.data;
          this.saveToLocalStorage();
        }
      }
    } catch (error) {
      console.error('更新AI配置失败:', error);
    }
  }

  // 添加AI配置
  public async addConfig(type: 'text' | 'vision', config: Omit<AIConfig, 'id' | 'createdAt' | 'updatedAt' | 'errorCount' | 'totalRequests' | 'successRequests'>): Promise<string> {
    try {
      const response = await fetch('/api/ai/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, config })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 重新加载配置
          await this.loadFromServer();
          return data.configId;
        }
      }
    } catch (error) {
      console.error('添加AI配置失败:', error);
    }
    throw new Error('添加配置失败');
  }

  // 更新AI配置
  public async updateConfig(type: 'text' | 'vision', configId: string, updates: Partial<AIConfig>): Promise<boolean> {
    try {
      const response = await fetch(`/api/ai/config/${configId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, updates })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 重新加载配置
          await this.loadFromServer();
          return true;
        }
      }
    } catch (error) {
      console.error('更新AI配置失败:', error);
    }
    return false;
  }

  // 删除AI配置
  public async deleteConfig(type: 'text' | 'vision', configId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/ai/config/${configId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 重新加载配置
          await this.loadFromServer();
          return true;
        }
      }
    } catch (error) {
      console.error('删除AI配置失败:', error);
    }
    return false;
  }

  // 获取指定类型的配置组
  public getConfigGroup(type: 'text' | 'vision'): AIConfigGroup {
    const settings = this.getSettingsSync();
    return type === 'text' ? settings.textAI : settings.visionAI;
  }

  // 更新配置组设置
  public async updateConfigGroup(type: 'text' | 'vision', updates: Partial<AIConfigGroup>): Promise<void> {
    const currentSettings = await this.getSettings();
    const newSettings: Partial<AISettings> = {};

    if (type === 'text') {
      newSettings.textAI = { ...currentSettings.textAI, ...updates };
    } else {
      newSettings.visionAI = { ...currentSettings.visionAI, ...updates };
    }

    await this.updateSettings(newSettings);
  }

  // 根据策略选择配置
  public selectConfig(type: 'text' | 'vision', options?: AICallOptions): AIConfig | null {
    const group = this.getConfigGroup(type);
    const availableConfigs = group.configs.filter(c => c.enabled);

    if (availableConfigs.length === 0) return null;

    // 如果指定了配置ID
    if (options?.forceConfigId) {
      return availableConfigs.find(c => c.id === options.forceConfigId) || null;
    }

    switch (group.strategy) {
      case 'manual':
        return availableConfigs[0] || null;

      case 'round_robin':
        const config = availableConfigs[group.currentIndex % availableConfigs.length];
        group.currentIndex = (group.currentIndex + 1) % availableConfigs.length;
        this.saveSettings();
        return config;

      case 'failover':
        // 按优先级排序，选择第一个可用的
        return availableConfigs
          .sort((a, b) => a.priority - b.priority)
          .find(c => c.errorCount < 5) || availableConfigs[0];

      case 'random':
        return availableConfigs[Math.floor(Math.random() * availableConfigs.length)];

      case 'priority':
        return availableConfigs
          .sort((a, b) => a.priority - b.priority)[0];

      default:
        return availableConfigs[0];
    }
  }

  // 记录API调用结果
  public async recordCallResult(configId: string, success: boolean, responseTime?: number): Promise<void> {
    try {
      const response = await fetch('/api/ai/config/record', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ configId, success, responseTime })
      });

      if (response.ok) {
        // 重新加载配置以获取最新统计
        await this.loadFromServer();
      }
    } catch (error) {
      console.error('记录API调用结果失败:', error);
    }
  }

  // 获取配置统计信息
  public getConfigStats(type?: 'text' | 'vision'): ConfigStats[] {
    let configs: AIConfig[] = [];
    
    if (type === 'text') {
      configs = this.settings.textAI.configs;
    } else if (type === 'vision') {
      configs = this.settings.visionAI.configs;
    } else {
      configs = [...this.settings.textAI.configs, ...this.settings.visionAI.configs];
    }

    return configs.map(config => ({
      configId: config.id,
      name: config.name,
      totalRequests: config.totalRequests,
      successRequests: config.successRequests,
      errorRequests: config.totalRequests - config.successRequests,
      successRate: config.totalRequests > 0 ? (config.successRequests / config.totalRequests) * 100 : 0,
      averageResponseTime: 0, // TODO: 实现响应时间统计
      lastUsed: config.lastUsed,
      status: !config.enabled ? 'disabled' : 
              config.errorCount >= 5 ? 'error' : 
              config.totalRequests > 0 ? 'active' : 'inactive'
    }));
  }

  // 验证配置
  public async validateConfig(config: AIConfig): Promise<ConfigValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本验证
    if (!config.name.trim()) errors.push('配置名称不能为空');
    if (!config.apiKey.trim()) errors.push('API密钥不能为空');
    if (!config.baseURL.trim()) errors.push('API地址不能为空');
    if (!config.model.trim()) errors.push('模型名称不能为空');

    // URL格式验证
    try {
      new URL(config.baseURL);
    } catch {
      errors.push('API地址格式不正确');
    }

    if (errors.length > 0) {
      return { valid: false, errors, warnings };
    }

    // TODO: 实际API测试
    // 这里可以发送一个测试请求来验证配置是否有效

    return { valid: true, errors, warnings };
  }

  // 从模板创建配置
  public createFromTemplate(templateIndex: number, apiKey: string): Omit<AIConfig, 'id' | 'createdAt' | 'updatedAt' | 'errorCount' | 'totalRequests' | 'successRequests'> {
    const template = AI_CONFIG_TEMPLATES[templateIndex];
    if (!template) throw new Error('模板不存在');

    return {
      ...template,
      apiKey,
      enabled: true,
      priority: 1
    } as Omit<AIConfig, 'id' | 'createdAt' | 'updatedAt' | 'errorCount' | 'totalRequests' | 'successRequests'>;
  }

  // 导出配置
  public exportSettings(): string {
    const exportData = {
      ...this.settings,
      // 移除敏感信息
      textAI: {
        ...this.settings.textAI,
        configs: this.settings.textAI.configs.map(c => ({ ...c, apiKey: '***' }))
      },
      visionAI: {
        ...this.settings.visionAI,
        configs: this.settings.visionAI.configs.map(c => ({ ...c, apiKey: '***' }))
      }
    };
    return JSON.stringify(exportData, null, 2);
  }

  // 重置配置
  public resetSettings(): void {
    this.settings = { ...DEFAULT_AI_SETTINGS };
    this.saveSettings();
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// 导出单例实例
export const aiConfigManager = AIConfigManager.getInstance();
