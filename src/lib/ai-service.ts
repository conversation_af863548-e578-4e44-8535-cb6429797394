import axios, { AxiosError } from 'axios';
import { AIConfig, AICallResult, AICallOptions } from '@/types/ai-config';

// 增强的AI服务基类
export abstract class BaseAIService {
  protected async makeRequest<T>(
    config: AIConfig,
    requestData: any,
    options?: AICallOptions
  ): Promise<AICallResult<T>> {
    const startTime = Date.now();
    let retryCount = 0;
    const maxRetries = options?.maxRetries ?? 3;

    while (retryCount <= maxRetries) {
      try {
        // 验证和清理URL
        const baseURL = config.baseURL?.trim();
        if (!baseURL) {
          throw new Error('API地址不能为空');
        }

        // 确保URL格式正确
        let apiUrl: string;
        try {
          const url = new URL(baseURL);
          apiUrl = `${url.origin}${url.pathname}`;
          if (!apiUrl.endsWith('/')) {
            apiUrl += '/';
          }
          apiUrl += 'chat/completions';
        } catch (urlError) {
          throw new Error(`无效的API地址: ${baseURL}`);
        }

        const response = await axios.post(
          apiUrl,
          requestData,
          {
            headers: {
              'Authorization': `Bearer ${config.apiKey}`,
              'Content-Type': 'application/json'
            },
            timeout: options?.timeout ?? config.timeout ?? 30000
          }
        );

        const responseTime = Date.now() - startTime;
        
        return {
          success: true,
          data: response.data,
          configId: config.id,
          responseTime,
          retryCount
        };

      } catch (error) {
        retryCount++;
        const responseTime = Date.now() - startTime;

        // 如果是最后一次重试或者不应该重试的错误
        if (retryCount > maxRetries || this.shouldNotRetry(error)) {
          return {
            success: false,
            error: this.getErrorMessage(error),
            configId: config.id,
            responseTime,
            retryCount: retryCount - 1
          };
        }

        // 等待后重试
        await this.delay(1000 * retryCount);
      }
    }

    return {
      success: false,
      error: '达到最大重试次数',
      configId: config.id,
      retryCount: maxRetries
    };
  }

  private shouldNotRetry(error: any): boolean {
    if (error instanceof AxiosError) {
      // 4xx 错误通常不应该重试（除了429）
      if (error.response?.status && error.response.status >= 400 && error.response.status < 500) {
        return error.response.status !== 429; // 429 (Rate Limit) 可以重试
      }
    }
    return false;
  }

  private getErrorMessage(error: any): string {
    if (error instanceof AxiosError) {
      if (error.response?.status === 429) {
        return 'API调用频率限制，请稍后重试';
      }
      if (error.response?.status === 401) {
        return 'API密钥无效或已过期';
      }
      if (error.response?.status === 403) {
        return 'API访问被拒绝，请检查权限';
      }
      if (error.response?.status >= 500) {
        return 'API服务器错误，请稍后重试';
      }
      if (error.code === 'ECONNABORTED') {
        return '请求超时，请检查网络连接';
      }
      return error.response?.data?.error?.message || error.message || 'API调用失败';
    }
    return error.message || '未知错误';
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 增强的视觉AI服务
export class EnhancedVisionAI extends BaseAIService {
  async analyzeImage(
    imageBase64: string,
    config: AIConfig,
    options?: AICallOptions
  ): Promise<AICallResult<any>> {
    const requestData = {
      model: config.model,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `请分析这张学习笔记图片，重点关注笔迹和学习内容。

请按以下JSON格式返回分析结果：
{
  "subject": "科目名称（必须是：数学、英语、政治、计算机408 中的一个）",
  "content": "学习内容的详细描述",
  "handwriting_analysis": "笔迹分析（整洁度、修改痕迹等）",
  "accuracy_estimate": "正确率估计（如果能判断的话）",
  "study_quality": "学习质量评估",
  "suggestions": "学习建议"
}

**重要：subject字段必须严格匹配以下选项之一：**
- 数学
- 英语  
- 政治
- 计算机408

**再次强调：subject字段必须是：数学、英语、政治、计算机408 中的一个，不能是其他值！**`
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`
              }
            }
          ]
        }
      ],
      max_tokens: config.maxTokens || 1000,
      temperature: config.temperature || 0.7
    };

    const result = await this.makeRequest(config, requestData, options);
    
    if (result.success && result.data) {
      try {
        const content = result.data.choices[0].message.content;
        const analysis = JSON.parse(content);
        return { ...result, data: analysis };
      } catch {
        // 如果不是 JSON 格式，返回原始文本
        return {
          ...result,
          data: {
            subject: '未知',
            content: result.data.choices[0].message.content,
            confidence: 0.5
          }
        };
      }
    }

    return result;
  }
}

// 增强的文本AI服务
export class EnhancedTextAI extends BaseAIService {
  async generateSummary(
    studyData: Record<string, string>,
    config: AIConfig,
    options?: AICallOptions
  ): Promise<AICallResult<string>> {
    const subjects = Object.keys(studyData).filter(key => studyData[key] && studyData[key].trim());
    
    if (subjects.length === 0) {
      return { success: false, error: '没有学习内容可以总结' };
    }

    const prompt = `请基于以下学习内容生成一份简洁、有条理的学习总结：

${subjects.map(subject => `${subject}:\n${studyData[subject]}`).join('\n\n')}

请生成一份包含以下内容的总结：
1. 今日学习概览
2. 各科目重点内容
3. 知识点关联分析
4. 学习建议

总结应该简洁明了，重点突出，有助于复习和巩固。请使用纯文本格式，不要使用markdown语法。`;

    const requestData = {
      model: config.model,
      messages: [
        {
          role: 'system',
          content: '你是一个专业的学习助手，擅长分析和总结学习内容，帮助学生更好地理解和记忆知识点。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: config.maxTokens || 1500,
      temperature: config.temperature || 0.7
    };

    const result = await this.makeRequest(config, requestData, options);
    
    if (result.success && result.data) {
      const summary = result.data.choices[0].message.content;
      return { ...result, data: summary };
    }

    return result;
  }

  async generateBriefRecord(
    analysisResult: any,
    config: AIConfig,
    options?: AICallOptions
  ): Promise<AICallResult<string>> {
    const requestData = {
      model: config.model,
      messages: [
        {
          role: 'user',
          content: `根据学习内容生成5-10字的简洁记录：

科目：${analysisResult.mappedSubject || analysisResult.subject}
内容：${analysisResult.content}

示例：
- 做了20xx年的英语阅读题，从第几页到第几页
- 背了xx个单词
- 完成数学作业，约x个题
- 复习政治知识

请只返回简洁记录：`
        }
      ],
      max_tokens: 20,
      temperature: 0.1
    };

    const result = await this.makeRequest(config, requestData, options);
    
    if (result.success && result.data) {
      let briefRecord = result.data.choices[0].message.content.trim();
      
      // 如果AI返回空或无效内容，使用规则生成
      if (!briefRecord || briefRecord.length < 3) {
        briefRecord = this.generateBriefRecordByRules(analysisResult);
      }
      
      return { ...result, data: briefRecord };
    }

    return result;
  }

  async formatDetailedAnalysis(
    analysisResult: any,
    config: AIConfig,
    options?: AICallOptions
  ): Promise<AICallResult<string>> {
    const requestData = {
      model: config.model,
      messages: [
        {
          role: 'user',
          content: `请分析以下图片AI的分析结果，并重新整理成简洁易读的文字格式：

${JSON.stringify(analysisResult, null, 2)}

请按以下格式输出：

**学习内容：**
[根据AI分析结果描述学习的具体内容]

**笔迹分析：**
[描述笔迹情况、修改痕迹等]

**学习评估：**
[描述正确率、学习效果等]

请确保输出是纯文字格式，不要包含JSON。`
        }
      ],
      max_tokens: 400,
      temperature: 0.1
    };

    const result = await this.makeRequest(config, requestData, options);
    
    if (result.success && result.data) {
      const formattedContent = result.data.choices[0].message.content.trim();
      
      // 如果AI返回空内容，使用备用格式
      if (!formattedContent || formattedContent.length < 10) {
        const fallbackContent = this.generateFallbackContent(analysisResult);
        return { ...result, data: fallbackContent };
      }
      
      return { ...result, data: formattedContent };
    }

    return result;
  }

  // 规则生成简要记录
  private generateBriefRecordByRules(analysisResult: any): string {
    const subject = analysisResult.mappedSubject || analysisResult.subject || '学习';
    const content = analysisResult.content || '';
    
    // 根据内容长度和科目生成简要记录
    if (content.length > 100) {
      return `完成${subject}练习题`;
    } else if (content.length > 50) {
      return `${subject}学习记录`;
    } else {
      return `${subject}笔记`;
    }
  }

  // 生成备用格式化内容
  private generateFallbackContent(analysisResult: any): string {
    const subject = analysisResult.mappedSubject || analysisResult.subject || '未知科目';
    const handwritingAnalysis = analysisResult.handwriting_analysis || '笔迹分析信息不可用';
    const accuracyEstimate = analysisResult.accuracy_estimate || '正确率信息不可用';

    return `**学习内容：**
完成了${subject}相关的学习任务，包含图片分析和内容识别。

**笔迹分析：**
${handwritingAnalysis}

**学习评估：**
估计正确率：${accuracyEstimate}

**备注：**
详细分析内容已保存，可在Notion页面中查看完整信息。`;
  }
}

// 导出增强的AI服务实例
export const enhancedVisionAI = new EnhancedVisionAI();
export const enhancedTextAI = new EnhancedTextAI();
