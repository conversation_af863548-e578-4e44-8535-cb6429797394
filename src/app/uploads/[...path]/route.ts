import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const filePath = params.path.join('/');
    
    // 首先尝试 public/uploads
    const publicPath = path.join(process.cwd(), 'public', 'uploads', filePath);
    let fileBuffer: Buffer;
    let exists = false;
    
    if (fs.existsSync(publicPath)) {
      fileBuffer = fs.readFileSync(publicPath);
      exists = true;
    } else {
      // 如果 public/uploads 不存在，尝试根目录的 uploads
      const fallbackPath = path.join(process.cwd(), 'uploads', filePath);
      if (fs.existsSync(fallbackPath)) {
        fileBuffer = fs.readFileSync(fallbackPath);
        exists = true;
      }
    }
    
    if (!exists) {
      return new NextResponse('File not found', { status: 404 });
    }
    
    // 确定 Content-Type
    const ext = path.extname(filePath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
    }
    
    return new NextResponse(fileBuffer!, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000',
      },
    });
  } catch (error) {
    console.error('读取文件失败:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
