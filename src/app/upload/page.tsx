'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Upload, 
  ArrowLeft, 
  Image as ImageIcon, 
  CheckCircle, 
  AlertCircle,
  X,
  Eye
} from 'lucide-react';

interface AnalysisResult {
  subject: string;
  content: string;
  confidence: number;
  handwriting_analysis?: string;
  accuracy_estimate?: string;
  corrections_found?: boolean;
  briefRecord?: string;
  mappedSubject?: string;
}

export default function UploadPage() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const handleFileSelect = (files: FileList | File[]) => {
    const fileArray = Array.isArray(files) ? files : Array.from(files);
    const validFiles: File[] = [];
    const newPreviewUrls: string[] = [];

    fileArray.forEach(file => {
      if (!file.type.startsWith('image/')) {
        setError('请只选择图片文件');
        return;
      }

      if (file.size > 10 * 1024 * 1024) {
        setError('图片文件过大，请选择小于10MB的图片');
        return;
      }

      validFiles.push(file);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        newPreviewUrls.push(e.target?.result as string);
        if (newPreviewUrls.length === validFiles.length) {
          setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
        }
      };
      reader.readAsDataURL(file);
    });

    if (validFiles.length > 0) {
      setSelectedFiles(prev => [...prev, ...validFiles]);
      setError('');
      setSuccess('');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    setIsUploading(true);
    setError('');
    setSuccess('');
    setUploadProgress(0);

    const results: AnalysisResult[] = [];
    const totalFiles = selectedFiles.length;

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setUploadProgress(Math.round((i / totalFiles) * 100));

        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          results.push(data.data.analysis);
        } else {
          console.error(`文件 ${file.name} 上传失败:`, data.message);
        }
      }

      setUploadProgress(100);
      setAnalysisResults(results);

      if (results.length > 0) {
        setSuccess(`成功处理 ${results.length}/${totalFiles} 张图片，内容已保存到 Notion。`);
      } else {
        setError('所有图片处理失败');
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const clearFiles = () => {
    // 清理预览URLs以释放内存
    previewUrls.forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
    });

    setSelectedFiles([]);
    setPreviewUrls([]);
    setAnalysisResults([]);
    setError('');
    setSuccess('');
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (index: number) => {
    const urlToRevoke = previewUrls[index];
    if (urlToRevoke && urlToRevoke.startsWith('blob:')) {
      URL.revokeObjectURL(urlToRevoke);
    }

    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setPreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={() => router.push('/dashboard')}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>返回</span>
            </button>
            <h1 className="ml-4 text-xl font-bold text-gray-900 dark:text-white">
              图片上传分析
            </h1>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 上传区域 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              上传学习图片
            </h2>
            
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-blue-500 dark:hover:border-blue-400 transition-colors cursor-pointer mb-4"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">
                拖拽图片到这里，或点击选择文件
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                支持多张图片，JPG、PNG、GIF 格式，每张最大 10MB
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                💡 简要记录将保存到科目单元格，详细分析将保存到总览页面
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                className="hidden"
              />
            </div>

            {/* 已选择的文件列表 */}
            {selectedFiles.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-md font-medium text-gray-900 dark:text-white">
                    已选择 {selectedFiles.length} 张图片
                  </h3>
                  <button
                    onClick={clearFiles}
                    className="text-red-600 hover:text-red-700 text-sm"
                  >
                    清空所有
                  </button>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {previewUrls.map((url, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={url}
                        alt={`预览 ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg shadow"
                      />
                      <button
                        onClick={() => removeFile(index)}
                        className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-3 h-3" />
                      </button>
                      <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                        {selectedFiles[index]?.name.substring(0, 10)}...
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 上传按钮 */}
            {selectedFiles.length > 0 && (
              <div className="mt-4">
                <button
                  onClick={handleUpload}
                  disabled={isUploading}
                  className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isUploading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      AI 分析中... ({uploadProgress}%)
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 mr-2" />
                      开始 AI 分析 ({selectedFiles.length} 张图片)
                    </>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-3" />
                <p className="text-red-700 dark:text-red-400">{error}</p>
              </div>
            </div>
          )}

          {/* 成功提示 */}
          {success && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                <p className="text-green-700 dark:text-green-400">{success}</p>
              </div>
            </div>
          )}

          {/* 分析结果 */}
          {analysisResults.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                AI 分析结果 ({analysisResults.length} 张图片)
              </h3>

              <div className="space-y-6">
                {analysisResults.map((result, index) => (
                  <div key={index} className="border-b border-gray-200 dark:border-gray-700 last:border-b-0 pb-6 last:pb-0">
                    <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                      图片 {index + 1}
                    </h4>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          科目识别与记录
                        </label>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
                              {result.mappedSubject || result.subject}
                            </span>
                            {result.mappedSubject && result.mappedSubject !== result.subject && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                (原识别: {result.subject})
                              </span>
                            )}
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              置信度: {(result.confidence * 100).toFixed(1)}%
                            </span>
                          </div>
                          {result.briefRecord && (
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">单元格记录:</span>
                              <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded text-sm font-medium">
                                {result.briefRecord}
                              </span>
                            </div>
                          )}
                          {result.accuracy_estimate && (
                            <span className="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded text-xs">
                              正确率: {result.accuracy_estimate}
                            </span>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          分析内容
                        </label>
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                          <div className="text-gray-900 dark:text-white text-sm prose prose-sm max-w-none">
                            {/* 检查是否是JSON格式，如果是则不显示 */}
                            {result.content && !result.content.startsWith('{') && !result.content.startsWith('##') ? (
                              <p className="whitespace-pre-wrap">{result.content}</p>
                            ) : (
                              <p className="text-gray-500 italic">详细分析已保存到Notion页面中，请在Notion中查看完整内容。</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {result.handwriting_analysis && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            笔迹分析
                          </label>
                          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-3">
                            <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                              {result.handwriting_analysis}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => router.push('/dashboard')}
                    className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    返回首页
                  </button>
                  <button
                    onClick={() => router.push('/data')}
                    className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    查看数据
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
