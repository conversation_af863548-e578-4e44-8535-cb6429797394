'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { aiConfigManager } from '@/lib/ai-config-manager';
import {
  AISettings,
  AIConfig,
  CallStrategy,
  STRATEGY_DESCRIPTIONS,
  AI_CONFIG_TEMPLATES
} from '@/types/ai-config';
import {
  ArrowLeft,
  Settings,
  Plus,
  Edit,
  Trash2,
  Power,
  PowerOff,
  Activity,
  AlertCircle,
  CheckCircle,
  Zap,
  TestTube
} from 'lucide-react';

export default function AIConfigPage() {
  const [settings, setSettings] = useState<AISettings | null>(null);
  const [activeTab, setActiveTab] = useState<'text' | 'vision'>('text');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<AIConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [testingConfigs, setTestingConfigs] = useState<Set<string>>(new Set());
  const router = useRouter();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const currentSettings = await aiConfigManager.getSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error('加载设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStrategyChange = async (type: 'text' | 'vision', strategy: CallStrategy) => {
    try {
      await aiConfigManager.updateConfigGroup(type, { strategy });
      await loadSettings();
    } catch (error) {
      console.error('更新策略失败:', error);
    }
  };

  const handleDeleteConfig = async (type: 'text' | 'vision', configId: string) => {
    if (confirm('确定要删除这个配置吗？')) {
      try {
        await aiConfigManager.deleteConfig(type, configId);
        await loadSettings();
      } catch (error) {
        console.error('删除配置失败:', error);
        alert('删除配置失败，请重试');
      }
    }
  };

  const handleToggleConfig = async (type: 'text' | 'vision', configId: string, enabled: boolean) => {
    try {
      await aiConfigManager.updateConfig(type, configId, { enabled });
      await loadSettings();
    } catch (error) {
      console.error('更新配置失败:', error);
    }
  };

  const handleTestConfig = async (type: 'text' | 'vision', config: AIConfig) => {
    setTestingConfigs(prev => new Set(prev).add(config.id));

    try {
      const response = await fetch('/api/ai/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          config
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`✅ 配置测试成功！\n响应时间: ${result.responseTime}ms`);
        // 更新配置统计
        await aiConfigManager.recordCallResult(config.id, true, result.responseTime);
        await loadSettings();
      } else {
        alert(`❌ 配置测试失败：\n${result.error || '未知错误'}`);
        // 记录失败
        await aiConfigManager.recordCallResult(config.id, false);
        await loadSettings();
      }
    } catch (error) {
      alert(`❌ 测试请求失败：\n${error instanceof Error ? error.message : '网络错误'}`);
      await aiConfigManager.recordCallResult(config.id, false);
      await loadSettings();
    } finally {
      setTestingConfigs(prev => {
        const newSet = new Set(prev);
        newSet.delete(config.id);
        return newSet;
      });
    }
  };

  if (loading || !settings) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载配置中...</p>
        </div>
      </div>
    );
  }

  const currentGroup = activeTab === 'text' ? settings.textAI : settings.visionAI;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={() => router.push('/dashboard')}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>返回</span>
            </button>
            <h1 className="ml-4 text-xl font-bold text-gray-900 dark:text-white">
              AI 配置管理
            </h1>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面描述 */}
          <div className="mb-6">
            <p className="text-gray-600 dark:text-gray-400">管理文本AI和视觉AI的配置，支持多配置和故障恢复</p>
          </div>

          {/* 标签页 */}
          <div className="mb-6">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('text')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'text'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  文本AI配置 ({settings.textAI.configs.length})
                </button>
                <button
                  onClick={() => setActiveTab('vision')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'vision'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  视觉AI配置 ({settings.visionAI.configs.length})
                </button>
              </nav>
            </div>
          </div>

          {/* 策略设置 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
              调用策略
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  当前策略
                </label>
                <select
                  value={currentGroup.strategy}
                  onChange={(e) => handleStrategyChange(activeTab, e.target.value as CallStrategy)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Object.entries(STRATEGY_DESCRIPTIONS).map(([key, desc]) => (
                    <option key={key} value={key}>{desc}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  故障转移设置
                </label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={currentGroup.fallbackEnabled}
                      onChange={(e) => aiConfigManager.updateConfigGroup(activeTab, { fallbackEnabled: e.target.checked })}
                      className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">启用故障转移</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* 配置列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <Settings className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" />
                {activeTab === 'text' ? '文本AI' : '视觉AI'}配置列表
              </h2>
              <button
                onClick={() => setShowAddModal(true)}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>添加配置</span>
              </button>
            </div>

            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {currentGroup.configs.length === 0 ? (
                <div className="px-6 py-12 text-center">
                  <Settings className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">暂无配置，点击"添加配置"开始设置</p>
                </div>
              ) : (
                currentGroup.configs.map((config) => (
                  <ConfigItem
                    key={config.id}
                    config={config}
                    type={activeTab}
                    onEdit={setEditingConfig}
                    onDelete={() => handleDeleteConfig(activeTab, config.id)}
                    onToggle={(enabled) => handleToggleConfig(activeTab, config.id, enabled)}
                    onTest={() => handleTestConfig(activeTab, config)}
                    isTesting={testingConfigs.has(config.id)}
                  />
                ))
              )}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Activity className="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
              使用统计
            </h2>
            <ConfigStats type={activeTab} />
          </div>
        </div>
      </main>

      {/* 添加/编辑配置模态框 */}
      {(showAddModal || editingConfig) && (
        <ConfigModal
          config={editingConfig}
          type={activeTab}
          onClose={() => {
            setShowAddModal(false);
            setEditingConfig(null);
          }}
          onSave={() => {
            setShowAddModal(false);
            setEditingConfig(null);
            loadSettings();
          }}
        />
      )}
    </div>
  );
}

// 配置项组件
function ConfigItem({
  config,
  type,
  onEdit,
  onDelete,
  onToggle,
  onTest,
  isTesting
}: {
  config: AIConfig;
  type: 'text' | 'vision';
  onEdit: (config: AIConfig) => void;
  onDelete: () => void;
  onToggle: (enabled: boolean) => void;
  onTest: () => void;
  isTesting: boolean;
}) {
  const stats = aiConfigManager.getConfigStats(type).find(s => s.configId === config.id);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'disabled':
        return <PowerOff className="w-4 h-4 text-gray-400" />;
      default:
        return <Power className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={config.enabled}
                onChange={(e) => onToggle(e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div className="flex items-center space-x-2">
              {stats && getStatusIcon(stats.status)}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">{config.name}</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {config.provider} • {config.model} • 优先级: {config.priority}
                </p>
              </div>
            </div>
          </div>

          {stats && (
            <div className="mt-2 ml-8 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <span className={`px-2 py-1 rounded-full text-xs ${
                stats.successRate >= 80 ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                stats.successRate >= 50 ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
              }`}>
                成功率: {stats.successRate.toFixed(1)}%
              </span>
              <span>总请求: {stats.totalRequests}</span>
              <span>状态: {getStatusText(stats.status)}</span>
              {stats.lastUsed && (
                <span>最后使用: {new Date(stats.lastUsed).toLocaleString()}</span>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={onTest}
            disabled={isTesting || !config.enabled}
            className="flex items-center space-x-1 px-3 py-1 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900 rounded-md text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTesting ? (
              <>
                <div className="w-3 h-3 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                <span>测试中</span>
              </>
            ) : (
              <>
                <TestTube className="w-3 h-3" />
                <span>测试</span>
              </>
            )}
          </button>
          <button
            onClick={() => onEdit(config)}
            className="flex items-center space-x-1 px-3 py-1 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-md text-sm transition-colors"
          >
            <Edit className="w-3 h-3" />
            <span>编辑</span>
          </button>
          <button
            onClick={onDelete}
            className="flex items-center space-x-1 px-3 py-1 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 rounded-md text-sm transition-colors"
          >
            <Trash2 className="w-3 h-3" />
            <span>删除</span>
          </button>
        </div>
      </div>
    </div>
  );
}

// 统计信息组件
function ConfigStats({ type }: { type: 'text' | 'vision' }) {
  const stats = aiConfigManager.getConfigStats(type);

  if (stats.length === 0) {
    return (
      <div className="text-center py-8">
        <Activity className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
        <p className="text-gray-500 dark:text-gray-400">暂无统计数据</p>
      </div>
    );
  }

  const totalRequests = stats.reduce((sum, s) => sum + s.totalRequests, 0);
  const totalSuccess = stats.reduce((sum, s) => sum + s.successRequests, 0);
  const overallSuccessRate = totalRequests > 0 ? (totalSuccess / totalRequests) * 100 : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{totalRequests}</div>
        <div className="text-sm text-gray-500 dark:text-gray-400">总请求数</div>
      </div>
      <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className={`text-2xl font-bold ${
          overallSuccessRate >= 80 ? 'text-green-600 dark:text-green-400' :
          overallSuccessRate >= 50 ? 'text-yellow-600 dark:text-yellow-400' :
          'text-red-600 dark:text-red-400'
        }`}>
          {overallSuccessRate.toFixed(1)}%
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">总成功率</div>
      </div>
      <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
          {stats.filter(s => s.status === 'active').length}
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">活跃配置</div>
      </div>
    </div>
  );
}

// 配置模态框组件
function ConfigModal({
  config,
  type,
  onClose,
  onSave
}: {
  config: AIConfig | null;
  type: 'text' | 'vision';
  onClose: () => void;
  onSave: () => void;
}) {
  const [formData, setFormData] = useState({
    name: config?.name || '',
    provider: config?.provider || 'custom',
    apiKey: config?.apiKey || '',
    baseURL: config?.baseURL || '',
    model: config?.model || '',
    priority: config?.priority || 1,
    maxTokens: config?.maxTokens || 4000,
    temperature: config?.temperature || 0.7,
    timeout: config?.timeout || 30000,
    enabled: config?.enabled ?? true
  });
  const [showTemplates, setShowTemplates] = useState(false);
  const [validating, setValidating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidating(true);

    try {
      if (config) {
        // 编辑现有配置
        await aiConfigManager.updateConfig(type, config.id, formData);
      } else {
        // 添加新配置
        await aiConfigManager.addConfig(type, formData);
      }
      onSave();
    } catch (error) {
      console.error('保存配置失败:', error);
      alert('保存配置失败，请检查输入');
    } finally {
      setValidating(false);
    }
  };

  const handleTemplateSelect = (templateIndex: number) => {
    const template = aiConfigManager.createFromTemplate(templateIndex, formData.apiKey);
    setFormData({ ...formData, ...template });
    setShowTemplates(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {config ? '编辑配置' : '添加配置'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {!config && (
            <div className="mb-4">
              <button
                type="button"
                onClick={() => setShowTemplates(!showTemplates)}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
              >
                {showTemplates ? '隐藏模板' : '使用预设模板'}
              </button>

              {showTemplates && (
                <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                  {AI_CONFIG_TEMPLATES.map((template, index) => (
                    <button
                      key={index}
                      onClick={() => handleTemplateSelect(index)}
                      className="text-left p-3 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="font-medium text-sm text-gray-900 dark:text-white">{template.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{template.provider} • {template.model}</div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  配置名称 *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如：OpenAI GPT-4"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  提供商
                </label>
                <select
                  value={formData.provider}
                  onChange={(e) => setFormData({ ...formData, provider: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="xrsite">XRSite</option>
                  <option value="custom">自定义</option>
                  <option value="other">其他</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                API密钥 *
              </label>
              <input
                type="password"
                required
                value={formData.apiKey}
                onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="sk-..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                API地址 *
              </label>
              <input
                type="url"
                required
                value={formData.baseURL}
                onChange={(e) => setFormData({ ...formData, baseURL: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://api.openai.com/v1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                模型名称 *
              </label>
              <input
                type="text"
                required
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="gpt-4"
              />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  优先级
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.priority || 1}
                  onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 1 })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  最大Token
                </label>
                <input
                  type="number"
                  min="100"
                  max="32000"
                  value={formData.maxTokens || 4000}
                  onChange={(e) => setFormData({ ...formData, maxTokens: parseInt(e.target.value) || 4000 })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  温度
                </label>
                <input
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={formData.temperature || 0.7}
                  onChange={(e) => setFormData({ ...formData, temperature: parseFloat(e.target.value) || 0.7 })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  超时(ms)
                </label>
                <input
                  type="number"
                  min="5000"
                  max="120000"
                  step="1000"
                  value={formData.timeout || 30000}
                  onChange={(e) => setFormData({ ...formData, timeout: parseInt(e.target.value) || 30000 })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="enabled"
                checked={formData.enabled}
                onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="enabled" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                启用此配置
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={validating}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {validating ? '验证中...' : '保存'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '未使用',
    error: '错误',
    disabled: '已禁用'
  };
  return statusMap[status] || status;
}
