import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { notionService } from '@/lib/notion';

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    // 获取数据库结构
    const result = await notionService.getDatabaseSchema();

    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.message || '获取数据库结构失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      schema: result.schema,
      propertyNames: Object.keys(result.schema || {}),
      data: {
        databaseId: process.env.DATABASE_ID,
        hasToken: !!process.env.NOTION_TOKEN
      }
    });

  } catch (error) {
    console.error('获取数据库结构错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
