import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { enhancedVisionAI, enhancedTextAI } from '@/lib/ai-service';
import { AIConfig, AICallOptions } from '@/types/ai-config';

// AI调用API接口
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    const body = await request.json();
    const { 
      type, 
      action, 
      config, 
      data, 
      options 
    }: {
      type: 'text' | 'vision';
      action: string;
      config: AIConfig;
      data: any;
      options?: AICallOptions;
    } = body;

    if (!type || !action || !config) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数'
      }, { status: 400 });
    }

    let result;

    if (type === 'vision') {
      switch (action) {
        case 'analyzeImage':
          if (!data.imageBase64) {
            return NextResponse.json({
              success: false,
              message: '缺少图片数据'
            }, { status: 400 });
          }
          result = await enhancedVisionAI.analyzeImage(data.imageBase64, config, options);
          break;
        default:
          return NextResponse.json({
            success: false,
            message: '不支持的视觉AI操作'
          }, { status: 400 });
      }
    } else if (type === 'text') {
      switch (action) {
        case 'generateSummary':
          if (!data.studyData) {
            return NextResponse.json({
              success: false,
              message: '缺少学习数据'
            }, { status: 400 });
          }
          result = await enhancedTextAI.generateSummary(data.studyData, config, options);
          break;
        case 'generateBriefRecord':
          if (!data.analysisResult) {
            return NextResponse.json({
              success: false,
              message: '缺少分析结果'
            }, { status: 400 });
          }
          result = await enhancedTextAI.generateBriefRecord(data.analysisResult, config, options);
          break;
        case 'formatDetailedAnalysis':
          if (!data.analysisResult) {
            return NextResponse.json({
              success: false,
              message: '缺少分析结果'
            }, { status: 400 });
          }
          result = await enhancedTextAI.formatDetailedAnalysis(data.analysisResult, config, options);
          break;
        default:
          return NextResponse.json({
            success: false,
            message: '不支持的文本AI操作'
          }, { status: 400 });
      }
    } else {
      return NextResponse.json({
        success: false,
        message: '不支持的AI类型'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: result.success,
      data: result.data,
      error: result.error,
      metadata: {
        configId: result.configId,
        responseTime: result.responseTime,
        retryCount: result.retryCount,
        tokensUsed: result.tokensUsed
      }
    });

  } catch (error) {
    console.error('AI调用错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
