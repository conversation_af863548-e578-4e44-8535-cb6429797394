import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { database } from '@/lib/database';
import { AIConfig } from '@/types/ai-config';

// 获取AI配置
export async function GET(request: NextRequest) {
  try {
    requireAuth(request);

    const settings = database.getAISettings();
    
    return NextResponse.json({
      success: true,
      data: settings
    });

  } catch (error) {
    console.error('获取AI配置错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}

// 添加AI配置
export async function POST(request: NextRequest) {
  try {
    requireAuth(request);

    const { type, config }: { 
      type: 'text' | 'vision'; 
      config: Omit<AIConfig, 'id' | 'createdAt' | 'updatedAt' | 'errorCount' | 'totalRequests' | 'successRequests'> 
    } = await request.json();

    if (!type || !config) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数'
      }, { status: 400 });
    }

    const configId = database.addAIConfig(type, config);
    
    return NextResponse.json({
      success: true,
      configId,
      message: '配置添加成功'
    });

  } catch (error) {
    console.error('添加AI配置错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}

// 更新AI设置
export async function PUT(request: NextRequest) {
  try {
    requireAuth(request);

    const updates = await request.json();

    if (!updates) {
      return NextResponse.json({
        success: false,
        message: '缺少更新数据'
      }, { status: 400 });
    }

    database.updateAISettings(updates);
    const newSettings = database.getAISettings();
    
    return NextResponse.json({
      success: true,
      data: newSettings,
      message: '设置更新成功'
    });

  } catch (error) {
    console.error('更新AI设置错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
