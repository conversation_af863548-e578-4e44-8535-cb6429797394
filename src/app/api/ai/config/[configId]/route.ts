import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { database } from '@/lib/database';
import { AIConfig } from '@/types/ai-config';

// 更新AI配置
export async function PUT(
  request: NextRequest,
  { params }: { params: { configId: string } }
) {
  try {
    requireAuth(request);

    const { configId } = params;
    const { type, updates }: { 
      type: 'text' | 'vision'; 
      updates: Partial<AIConfig> 
    } = await request.json();

    if (!configId || !type || !updates) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数'
      }, { status: 400 });
    }

    const success = database.updateAIConfig(configId, updates);
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: '配置更新成功'
      });
    } else {
      return NextResponse.json({
        success: false,
        message: '配置不存在'
      }, { status: 404 });
    }

  } catch (error) {
    console.error('更新AI配置错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}

// 删除AI配置
export async function DELETE(
  request: NextRequest,
  { params }: { params: { configId: string } }
) {
  try {
    requireAuth(request);

    const { configId } = params;
    const { type }: { type: 'text' | 'vision' } = await request.json();

    if (!configId || !type) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数'
      }, { status: 400 });
    }

    const success = database.deleteAIConfig(configId);
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: '配置删除成功'
      });
    } else {
      return NextResponse.json({
        success: false,
        message: '配置不存在'
      }, { status: 404 });
    }

  } catch (error) {
    console.error('删除AI配置错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
