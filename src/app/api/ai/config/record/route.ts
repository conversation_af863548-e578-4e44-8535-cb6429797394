import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { database } from '@/lib/database';

// 记录API调用结果
export async function POST(request: NextRequest) {
  try {
    requireAuth(request);

    const { configId, success, responseTime }: { 
      configId: string; 
      success: boolean; 
      responseTime?: number 
    } = await request.json();

    if (!configId || success === undefined) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数'
      }, { status: 400 });
    }

    // 获取当前配置
    const allConfigs = [
      ...database.getAIConfigs('text'),
      ...database.getAIConfigs('vision')
    ];
    const config = allConfigs.find(c => c.id === configId);
    
    if (!config) {
      return NextResponse.json({
        success: false,
        message: '配置不存在'
      }, { status: 404 });
    }

    // 更新统计信息
    const updates: any = {
      totalRequests: config.totalRequests + 1,
      lastUsed: new Date()
    };

    if (success) {
      updates.successRequests = config.successRequests + 1;
      updates.errorCount = 0; // 重置错误计数
    } else {
      updates.errorCount = config.errorCount + 1;
    }

    const updateSuccess = database.updateAIConfig(configId, updates);
    
    if (updateSuccess) {
      return NextResponse.json({
        success: true,
        message: '调用结果记录成功'
      });
    } else {
      return NextResponse.json({
        success: false,
        message: '记录失败'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('记录API调用结果错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
