import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { enhancedVisionAI, enhancedTextAI } from '@/lib/ai-service';
import { AIConfig } from '@/types/ai-config';

// AI配置测试API
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    const { type, config }: { type: 'text' | 'vision'; config: AIConfig } = await request.json();

    if (!type || !config) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数'
      }, { status: 400 });
    }

    const startTime = Date.now();
    let result;

    if (type === 'text') {
      // 测试文本AI配置
      const testData = {
        '测试': '这是一个简单的测试内容，用于验证AI配置是否正常工作。'
      };
      
      result = await enhancedTextAI.generateSummary(testData, config, {
        maxRetries: 1,
        timeout: 10000
      });
    } else {
      // 测试视觉AI配置
      // 创建一个简单的测试图片（1x1像素的白色PNG）
      const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
      
      result = await enhancedVisionAI.analyzeImage(testImageBase64, config, {
        maxRetries: 1,
        timeout: 10000
      });
    }

    const responseTime = Date.now() - startTime;

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: '配置测试成功',
        responseTime,
        data: {
          configId: result.configId,
          retryCount: result.retryCount || 0,
          hasData: !!result.data
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error || '测试失败',
        responseTime,
        data: {
          configId: result.configId,
          retryCount: result.retryCount || 0
        }
      });
    }

  } catch (error) {
    console.error('AI配置测试错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        error: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '服务器错误'
    }, { status: 500 });
  }
}
