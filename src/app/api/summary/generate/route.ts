import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { aiCaller } from '@/lib/ai-caller';
import { notionService } from '@/lib/notion';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    // 获取今日学习数据
    const todayDataResult = await notionService.getTodayData();

    if (!todayDataResult.success) {
      return NextResponse.json({
        success: false,
        message: '获取今日学习数据失败'
      }, { status: 500 });
    }

    const studyData = todayDataResult.data || {};

    // 检查是否有学习内容
    const hasContent = Object.values(studyData).some(content => 
      content && typeof content === 'string' && content.trim()
    );

    if (!hasContent) {
      return NextResponse.json({
        success: false,
        message: '今日暂无学习内容，无法生成总结'
      }, { status: 400 });
    }

    // 生成 AI 总结
    const summaryResult = await aiCaller.callTextAI('generateSummary', { studyData });

    if (!summaryResult.success) {
      return NextResponse.json({
        success: false,
        message: summaryResult.error || 'AI 总结生成失败'
      }, { status: 500 });
    }

    const summary = summaryResult.data;

    // 保存总结到 Notion
    const saveResult = await notionService.saveSummary(summary);

    if (!saveResult.success) {
      console.warn('保存总结到 Notion 失败:', saveResult.message);
      // 即使保存失败，也返回生成的总结
    }

    return NextResponse.json({
      success: true,
      summary,
      saved: saveResult.success,
      message: saveResult.success 
        ? 'AI 总结生成并保存成功' 
        : 'AI 总结生成成功，但保存失败',
      data: {
        date: todayDataResult.date,
        subjectsCount: Object.keys(studyData).filter(key => 
          studyData[key] && studyData[key].trim()
        ).length,
        totalCharacters: (Object.values(studyData) as string[])
          .filter(content => content && typeof content === 'string')
          .reduce((total: number, content: string) => total + content.length, 0)
      }
    });

  } catch (error) {
    console.error('生成 AI 总结错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
