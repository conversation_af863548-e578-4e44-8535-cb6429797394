import { NextRequest, NextResponse } from 'next/server';
import { textAI } from '@/lib/ai';

export async function GET(request: NextRequest) {
  try {
    console.log('测试文本AI...');
    
    // 测试简要记录生成
    const testAnalysis = {
      subject: "英语",
      content: "学生完成了英语阅读理解练习，包含多道选择题",
      handwriting_analysis: "笔迹整洁，有红笔修改",
      accuracy_estimate: "85%"
    };
    
    const briefResult = await textAI.generateBriefRecord(testAnalysis);
    console.log('简要记录结果:', briefResult);
    
    const formatResult = await textAI.formatDetailedAnalysis(testAnalysis);
    console.log('格式化结果:', formatResult);
    
    return NextResponse.json({
      success: true,
      briefResult,
      formatResult,
      env: {
        hasTextApiKey: !!process.env.TEXT_API_KEY,
        textBaseUrl: process.env.TEXT_BASE_URL,
        textModel: process.env.TEXT_MODEL
      }
    });
    
  } catch (error) {
    console.error('测试文本AI失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}
