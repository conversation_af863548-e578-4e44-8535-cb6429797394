// AI配置相关的类型定义

// AI服务提供商类型
export type AIProvider = 'openai' | 'anthropic' | 'custom' | 'xrsite' | 'other';

// 调用策略类型
export type CallStrategy = 'manual' | 'round_robin' | 'failover' | 'random' | 'priority';

// 单个AI配置
export interface AIConfig {
  id: string;
  name: string;
  provider: AIProvider;
  apiKey: string;
  baseURL: string;
  model: string;
  enabled: boolean;
  priority: number; // 优先级，数字越小优先级越高
  maxTokens?: number;
  temperature?: number;
  timeout?: number; // 超时时间（毫秒）
  rateLimit?: {
    requestsPerMinute: number;
    requestsPerHour: number;
  };
  lastUsed?: Date;
  errorCount: number; // 连续错误次数
  totalRequests: number; // 总请求数
  successRequests: number; // 成功请求数
  createdAt: Date;
  updatedAt: Date;
}

// AI配置组（文本AI或视觉AI）
export interface AIConfigGroup {
  type: 'text' | 'vision';
  strategy: CallStrategy;
  configs: AIConfig[];
  currentIndex: number; // 当前使用的配置索引（用于轮询）
  fallbackEnabled: boolean; // 是否启用故障转移
  maxRetries: number; // 最大重试次数
  retryDelay: number; // 重试延迟（毫秒）
}

// 完整的AI配置
export interface AISettings {
  textAI: AIConfigGroup;
  visionAI: AIConfigGroup;
  globalSettings: {
    enableLogging: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableMetrics: boolean;
    autoSwitchOnError: boolean; // 出错时自动切换
    healthCheckInterval: number; // 健康检查间隔（分钟）
  };
  version: string;
  lastUpdated: Date;
}

// API调用结果
export interface AICallResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  configId?: string; // 使用的配置ID
  responseTime?: number; // 响应时间（毫秒）
  tokensUsed?: number; // 使用的token数量
  retryCount?: number; // 重试次数
}

// API调用选项
export interface AICallOptions {
  maxRetries?: number;
  timeout?: number;
  forceConfigId?: string; // 强制使用指定配置
  skipFailover?: boolean; // 跳过故障转移
}

// 配置验证结果
export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  responseTime?: number;
}

// 配置统计信息
export interface ConfigStats {
  configId: string;
  name: string;
  totalRequests: number;
  successRequests: number;
  errorRequests: number;
  successRate: number;
  averageResponseTime: number;
  lastUsed?: Date;
  status: 'active' | 'inactive' | 'error' | 'disabled';
}

// 默认配置
export const DEFAULT_AI_SETTINGS: AISettings = {
  textAI: {
    type: 'text',
    strategy: 'failover',
    configs: [],
    currentIndex: 0,
    fallbackEnabled: true,
    maxRetries: 3,
    retryDelay: 1000
  },
  visionAI: {
    type: 'vision',
    strategy: 'failover',
    configs: [],
    currentIndex: 0,
    fallbackEnabled: true,
    maxRetries: 3,
    retryDelay: 1000
  },
  globalSettings: {
    enableLogging: true,
    logLevel: 'info',
    enableMetrics: true,
    autoSwitchOnError: true,
    healthCheckInterval: 30
  },
  version: '1.0.0',
  lastUpdated: new Date()
};

// 预设的AI配置模板
export const AI_CONFIG_TEMPLATES: Partial<AIConfig>[] = [
  {
    name: 'OpenAI GPT-4',
    provider: 'openai',
    baseURL: 'https://api.openai.com/v1',
    model: 'gpt-4',
    maxTokens: 4000,
    temperature: 0.7,
    timeout: 30000
  },
  {
    name: 'OpenAI GPT-4 Vision',
    provider: 'openai',
    baseURL: 'https://api.openai.com/v1',
    model: 'gpt-4-vision-preview',
    maxTokens: 4000,
    temperature: 0.7,
    timeout: 30000
  },
  {
    name: 'XRSite Free Text',
    provider: 'xrsite',
    baseURL: 'https://api.xrsite.online/v1',
    model: 'openai/gpt-oss-20b:free',
    maxTokens: 2000,
    temperature: 0.7,
    timeout: 30000,
    rateLimit: {
      requestsPerMinute: 10,
      requestsPerHour: 100
    }
  },
  {
    name: 'XRSite Free Vision',
    provider: 'xrsite',
    baseURL: 'https://api.xrsite.online/v1',
    model: 'qwen/qwen2.5-vl-72b-instruct:free',
    maxTokens: 2000,
    temperature: 0.7,
    timeout: 30000,
    rateLimit: {
      requestsPerMinute: 10,
      requestsPerHour: 100
    }
  }
];

// 策略描述
export const STRATEGY_DESCRIPTIONS: Record<CallStrategy, string> = {
  manual: '手动选择 - 使用指定的配置',
  round_robin: '轮询 - 依次使用每个配置',
  failover: '故障转移 - 按优先级使用，失败时切换到下一个',
  random: '随机 - 随机选择一个可用配置',
  priority: '优先级 - 始终使用优先级最高的可用配置'
};
