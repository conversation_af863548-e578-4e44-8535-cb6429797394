# 学习管理系统 2.0

[![Docker](https://img.shields.io/badge/Docker-Ready-blue?logo=docker)](https://hub.docker.com)
[![Next.js](https://img.shields.io/badge/Next.js-15.4.6-black?logo=next.js)](https://nextjs.org)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?logo=typescript)](https://www.typescriptlang.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

一个基于 Next.js + TypeScript 的现代化学习管理系统，支持图片 AI 分析、手动录入、数据查看和智能总结功能。

## ⚡ 一键部署

### 🚀 超简单部署（推荐）

```bash
# 一行命令部署（交互式配置）
curl -sSL https://raw.githubusercontent.com/your-repo/notion-ai-study/main/one-line-deploy.sh | bash
```

### 🛠️ 直接命令行部署

```bash
# 创建目录
mkdir -p notion-ai-study && cd notion-ai-study
mkdir -p uploads logs data

# 一键启动容器
docker run -d \
  --name notion-ai-study \
  -p 62211:5002 \
  -v /root/notion-ai-study/uploads:/app/uploads \
  -v /root/notion-ai-study/logs:/app/logs \
  -v /root/notion-ai-study/data:/app/data \
  --env-file .env \
  --restart unless-stopped \
  jhxxr/notion-ai-study:latest

# 访问应用: http://localhost:62211
```

### 📦 使用部署脚本（推荐）

```bash
# 下载部署脚本
wget https://raw.githubusercontent.com/your-repo/notion-ai-study/main/deploy.sh
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

部署脚本会自动：
- 创建必要的目录结构
- 生成环境变量模板
- 拉取最新镜像
- 启动容器并配置持久化存储

### 📁 数据持久化

容器会自动映射以下目录到宿主机：
- `/app/uploads` → `./uploads` - 上传的图片文件
- `/app/logs` → `./logs` - 应用日志
- `/app/data` → `./data` - SQLite数据库文件

这样即使重启容器，所有数据都会保留。

## 🔧 API 接口

- `GET /api/health` - 健康检查
- `POST /api/auth/login` - 用户登录
- `POST /api/upload` - 图片上传分析
- `POST /api/study/update` - 更新学习记录
- `GET /api/study/today` - 获取今日数据
- `POST /api/summary/generate` - 生成 AI 总结

## 🚀 快速命令

```bash
# 查看容器状态
docker ps -f name=notion-ai-study

# 查看日志
docker logs -f notion-ai-study

# 重启服务
docker restart notion-ai-study

# 停止服务
docker stop notion-ai-study

# 删除容器
docker rm -f notion-ai-study

# 更新到最新版本
docker pull jhxxr/notion-ai-study:latest
# 然后重新运行部署命令
```

## ⚙️ AI配置

首次部署后，需要配置AI服务：

1. **访问配置页面**：`http://localhost:62211/ai-config`
2. **添加AI配置**：
   - 点击"添加配置"按钮
   - 选择AI提供商（OpenAI、Anthropic、XRSite等）
   - 填入API密钥和相关配置
   - 设置调用策略（故障转移、轮询等）
3. **测试配置**：点击配置卡片上的"测试"按钮验证
4. **开始使用**：配置成功后即可使用AI功能

### 支持的AI服务

- **OpenAI**：GPT-4、GPT-4 Vision
- **Anthropic**：Claude系列
- **XRSite**：免费AI服务（有速率限制）
- **自定义**：兼容OpenAI API格式的服务

### 配置策略

- **故障转移**：按优先级使用，失败时自动切换
- **轮询**：依次使用每个配置，平衡负载
- **随机**：随机选择配置
- **手动**：使用指定配置

## 🏥 健康检查

```bash
# 检查应用状态
curl http://localhost:5002/api/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456,
  "environment": "production",
  "version": "0.1.0"
}
```

## 🛠️ 技术栈

- **Next.js 15.4.6** - 全栈 React 框架
- **TypeScript** - 类型安全
- **TailwindCSS 4** - 原子化 CSS 框架
- **Docker** - 容器化部署
- **GitHub Actions** - 自动化 CI/CD
- **Notion API** - 数据存储
- **OpenAI API** - AI 分析和总结

## ✨ 特性

- 🐳 **Docker 优化** - 多阶段构建，生产就绪
- 🚀 **一键部署** - 支持多种部署方式
- 🔧 **环境变量配置** - 灵活的AI模型配置
- 🏥 **健康检查** - 内置监控端点
- 🔒 **安全配置** - 非root用户运行
- 📊 **自动化CI/CD** - GitHub Actions集成

## 📄 许可证

MIT License

---

**学习管理系统 2.0** - 让学习更智能，让记录更简单！
