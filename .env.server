# 服务器环境变量模板
# 复制此文件为 .env 并填入实际值

# 用户认证配置
STUDY_USERNAME=your_username
STUDY_PASSWORD=your_password

# Notion API配置
NOTION_TOKEN=secret_your_notion_token
DATABASE_ID=your_database_id

# 图像识别AI配置
VISION_API_KEY=sk-your_vision_api_key
VISION_BASE_URL=https://api.xrsite.online/v1
VISION_MODEL=qwen/qwen2.5-vl-72b-instruct:free

# 文字处理AI配置
TEXT_API_KEY=sk-your_text_api_key
TEXT_BASE_URL=https://api.xrsite.online/v1
TEXT_MODEL=openai/gpt-oss-20b:free

# 服务器配置
DEBUG=false

# 文件上传配置
UPLOAD_FOLDER=./uploads
MAX_CONTENT_LENGTH=16777216

# DockerHub配置（用于docker-compose）
DOCKERHUB_USERNAME=your_dockerhub_username
