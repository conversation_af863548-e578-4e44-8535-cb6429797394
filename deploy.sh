#!/bin/bash

# Notion AI Study 部署脚本
# 使用方法: ./deploy.sh

set -e

echo "🚀 开始部署 Notion AI Study..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 创建项目目录
PROJECT_DIR="/root/notion-ai-study"
echo "📁 创建项目目录: $PROJECT_DIR"
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 创建必要的子目录
echo "📁 创建子目录..."
mkdir -p uploads logs data

# 创建环境变量文件（如果不存在）
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cat > .env << EOF
# Notion 配置
NOTION_TOKEN=your_notion_token_here
NOTION_DATABASE_ID=your_database_id_here

# 应用配置
NODE_ENV=production
PORT=5002

# 日志配置
LOG_LEVEL=info
EOF
    echo "⚠️  请编辑 .env 文件，填入正确的 Notion Token 和 Database ID"
    echo "   编辑命令: nano .env"
    read -p "按回车键继续..."
fi

# 停止并删除旧容器（如果存在）
if docker ps -a | grep -q notion-ai-study; then
    echo "🛑 停止旧容器..."
    docker stop notion-ai-study || true
    docker rm notion-ai-study || true
fi

# 拉取最新镜像
echo "📥 拉取最新镜像..."
docker pull jhxxr/notion-ai-study:latest

# 启动新容器
echo "🚀 启动容器..."
docker run -d \
  --name notion-ai-study \
  -p 62211:5002 \
  -v $PROJECT_DIR/uploads:/app/uploads \
  -v $PROJECT_DIR/logs:/app/logs \
  -v $PROJECT_DIR/data:/app/data \
  --env-file $PROJECT_DIR/.env \
  --restart unless-stopped \
  jhxxr/notion-ai-study:latest

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 5

# 检查容器状态
if docker ps | grep -q notion-ai-study; then
    echo "✅ 部署成功！"
    echo ""
    echo "🌐 访问地址: http://localhost:62211"
    echo "📊 容器状态: docker ps | grep notion-ai-study"
    echo "📋 查看日志: docker logs notion-ai-study"
    echo "🔧 进入容器: docker exec -it notion-ai-study /bin/bash"
    echo ""
    echo "📁 数据目录:"
    echo "   - 上传文件: $PROJECT_DIR/uploads"
    echo "   - 日志文件: $PROJECT_DIR/logs"
    echo "   - 数据库文件: $PROJECT_DIR/data"
    echo ""
    echo "⚙️  首次使用请访问 /ai-config 页面配置AI服务"
else
    echo "❌ 部署失败，请检查日志: docker logs notion-ai-study"
    exit 1
fi
